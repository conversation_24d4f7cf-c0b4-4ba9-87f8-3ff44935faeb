#!/usr/bin/env python3
"""
简化版自动套利扫描器 - 扫描10种主流加密货币，找出最大套利机会
"""

import requests
import time
from datetime import datetime
import pytz

def get_price(exchange, symbol, crypto_symbol):
    """获取单个交易所价格 - 支持全部11个交易所"""
    try:
        headers = {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"}

        if exchange == 'Binance':
            url = f"https://api.binance.com/api/v3/ticker/price?symbol={symbol}"
            response = requests.get(url, headers=headers, timeout=5)
            if response.status_code == 200:
                return float(response.json()['price'])

        elif exchange == 'OKX':
            url = f"https://www.okx.com/api/v5/market/ticker?instId={symbol}"
            response = requests.get(url, headers=headers, timeout=5)
            if response.status_code == 200:
                data = response.json()
                if data.get('data'):
                    return float(data['data'][0]['last'])

        elif exchange == 'Bybit':
            url = f"https://api.bybit.com/v5/market/tickers?category=spot&symbol={symbol}"
            response = requests.get(url, headers=headers, timeout=5)
            if response.status_code == 200:
                data = response.json()
                if data.get('result', {}).get('list'):
                    return float(data['result']['list'][0]['lastPrice'])

        elif exchange == 'MEXC':
            url = f"https://api.mexc.com/api/v3/ticker/price?symbol={symbol}"
            response = requests.get(url, headers=headers, timeout=5)
            if response.status_code == 200:
                return float(response.json()['price'])

        elif exchange == 'KuCoin':
            url = f"https://api.kucoin.com/api/v1/market/orderbook/level1?symbol={symbol}"
            response = requests.get(url, headers=headers, timeout=5)
            if response.status_code == 200:
                data = response.json()
                if data.get('code') == '200000':
                    return float(data['data']['price'])

        elif exchange == 'Gemini':
            url = f"https://api.gemini.com/v1/pubticker/{symbol.lower()}"
            response = requests.get(url, headers=headers, timeout=5)
            if response.status_code == 200:
                return float(response.json()['last'])

        elif exchange == 'Gate.io':
            url = f"https://api.gateio.ws/api/v4/spot/tickers?currency_pair={symbol}"
            response = requests.get(url, headers=headers, timeout=5)
            if response.status_code == 200:
                data = response.json()
                if data:
                    return float(data[0]['last'])

        elif exchange == 'Coinbase':
            # 使用Coinbase公开API
            url = f"https://api.coinbase.com/v2/exchange-rates?currency={crypto_symbol}"
            response = requests.get(url, headers=headers, timeout=5)
            if response.status_code == 200:
                data = response.json()
                if 'data' in data and 'rates' in data['data']:
                    usd_rate = data['data']['rates'].get('USD')
                    if usd_rate:
                        return float(usd_rate)

        elif exchange == 'Kraken':
            # 转换symbol格式
            kraken_symbol = symbol.replace('/', '').replace('-', '')
            if kraken_symbol == 'BTCUSD':
                kraken_symbol = 'XXBTZUSD'
            elif kraken_symbol == 'ETHUSD':
                kraken_symbol = 'XETHZUSD'
            elif kraken_symbol == 'ADAUSD':
                kraken_symbol = 'ADAUSD'
            elif kraken_symbol == 'SOLUSD':
                kraken_symbol = 'SOLUSD'
            elif kraken_symbol == 'DOTUSD':
                kraken_symbol = 'DOTUSD'
            elif kraken_symbol == 'AVAXUSD':
                kraken_symbol = 'AVAXUSD'

            url = f"https://api.kraken.com/0/public/Ticker?pair={kraken_symbol}"
            response = requests.get(url, headers=headers, timeout=5)
            if response.status_code == 200:
                data = response.json()
                if 'result' in data and data['result']:
                    pair_data = list(data['result'].values())[0]
                    if 'c' in pair_data:
                        return float(pair_data['c'][0])

        elif exchange == 'Bitfinex':
            # Bitfinex REST API
            url = f"https://api-pub.bitfinex.com/v2/ticker/t{symbol}"
            response = requests.get(url, headers=headers, timeout=5)
            if response.status_code == 200:
                data = response.json()
                if len(data) > 6:
                    return float(data[6])  # 最新价格

        elif exchange == 'Bitstamp':
            # Bitstamp REST API
            url = f"https://www.bitstamp.net/api/v2/ticker/{symbol.lower()}/"
            response = requests.get(url, headers=headers, timeout=5)
            if response.status_code == 200:
                data = response.json()
                if 'last' in data:
                    return float(data['last'])

    except Exception as e:
        print(f"   ⚠️  {exchange} 获取价格失败: {str(e)}")
        pass
    return None

def scan_crypto(crypto_symbol, crypto_name):
    """扫描单个加密货币 - 支持全部11个交易所"""
    print(f"\n🔍 正在扫描 {crypto_name} ({crypto_symbol})...")

    # 全部11个交易所和symbol格式配置
    exchanges_config = {
        'Binance': f'{crypto_symbol}USDT',
        'OKX': f'{crypto_symbol}-USDT',
        'Bybit': f'{crypto_symbol}USDT',
        'MEXC': f'{crypto_symbol}USDT',
        'KuCoin': f'{crypto_symbol}-USDT',
        'Gemini': f'{crypto_symbol}USD',
        'Gate.io': f'{crypto_symbol}_USDT',
        'Coinbase': f'{crypto_symbol}-USD',  # 特殊处理
        'Kraken': f'{crypto_symbol}USD',     # 特殊处理
        'Bitfinex': f'{crypto_symbol}USD',
        'Bitstamp': f'{crypto_symbol}USD'
    }
    
    prices = {}
    
    # 获取各交易所价格
    successful_exchanges = []
    failed_exchanges = []

    for exchange, symbol_format in exchanges_config.items():
        price = get_price(exchange, symbol_format, crypto_symbol)
        if price:
            prices[exchange] = price
            successful_exchanges.append(exchange)
        else:
            failed_exchanges.append(exchange)

    # 显示连接状态
    print(f"   ✅ 成功连接: {len(successful_exchanges)}/11 个交易所")
    if successful_exchanges:
        print(f"   📊 数据来源: {', '.join(successful_exchanges)}")
    if failed_exchanges:
        print(f"   ❌ 连接失败: {', '.join(failed_exchanges)}")
    
    if len(prices) >= 8:  # 要求至少8个交易所的数据
        price_values = list(prices.values())
        max_price = max(price_values)
        min_price = min(price_values)
        price_diff = max_price - min_price
        percentage_diff = (price_diff / min_price) * 100
        
        # 找出最高价和最低价的交易所
        max_exchange = next(ex for ex, price in prices.items() if price == max_price)
        min_exchange = next(ex for ex, price in prices.items() if price == min_price)
        
        print(f"   💰 发现套利机会: {percentage_diff:.3f}%")
        print(f"   📈 最高价: {max_exchange} ${max_price:.4f}")
        print(f"   📉 最低价: {min_exchange} ${min_price:.4f}")
        print(f"   💵 价差: ${price_diff:.4f}")
        print(f"   📊 数据源: {len(prices)} 个交易所")
        
        return {
            'crypto': crypto_symbol,
            'name': crypto_name,
            'max_exchange': max_exchange,
            'max_price': max_price,
            'min_exchange': min_exchange,
            'min_price': min_price,
            'price_diff': price_diff,
            'percentage_diff': percentage_diff,
            'exchange_count': len(prices),
            'prices': prices
        }
    else:
        print(f"   ❌ 数据不足 (仅获取到 {len(prices)}/11 个交易所数据，需要至少8个)")
        return None

def main():
    """主函数"""
    # 10种主流加密货币
    cryptocurrencies = {
        "BTC": "比特币",
        "ETH": "以太坊", 
        "BNB": "币安币",
        "XRP": "瑞波币",
        "ADA": "卡尔达诺",
        "DOGE": "狗狗币",
        "SOL": "索拉纳",
        "TRX": "波场",
        "DOT": "波卡",
        "AVAX": "雪崩"
    }
    
    print("🚀 自动套利扫描器启动！")
    print("="*80)
    print("正在扫描10种主流加密货币的套利机会...")
    print("="*80)
    
    try:
        while True:
            opportunities = []
            
            # 扫描所有加密货币
            for crypto_symbol, crypto_name in cryptocurrencies.items():
                result = scan_crypto(crypto_symbol, crypto_name)
                if result:
                    opportunities.append(result)
                time.sleep(1)  # 每种货币间隔1秒
            
            # 分析结果
            if opportunities:
                print("\n" + "="*80)
                print("📊 本轮套利机会排行榜")
                print("="*80)
                
                # 按套利百分比排序
                opportunities.sort(key=lambda x: x['percentage_diff'], reverse=True)
                
                # 显示前5名
                for i, opp in enumerate(opportunities[:5], 1):
                    print(f"{i}. 🏆 {opp['name']} ({opp['crypto']})")
                    print(f"   💎 套利空间: {opp['percentage_diff']:.3f}%")
                    print(f"   🔥 买入: {opp['min_exchange']} ${opp['min_price']:.4f}")
                    print(f"   💰 卖出: {opp['max_exchange']} ${opp['max_price']:.4f}")
                    print(f"   💵 利润: ${opp['price_diff']:.4f}")
                    print()
                
                # 显示最佳机会
                if opportunities:
                    best = opportunities[0]
                    print("🎯 " + "="*76)
                    print(f"🚀 最佳套利机会: {best['name']} ({best['crypto']})")
                    print(f"💎 套利空间: {best['percentage_diff']:.3f}%")
                    print("最高价格        | 最低价格          | 相差")
                    print(f"{best['max_exchange']}: ${best['max_price']:.4f}    | {best['min_exchange']}: ${best['min_price']:.4f} | ${best['price_diff']:.4f}")
                    print("🎯 " + "="*76)
            
            print("\n" + "="*80)
            print("⏱️  等待下一轮扫描...")
            print("="*80)
            time.sleep(30)  # 等待30秒进行下一轮扫描
            
    except KeyboardInterrupt:
        print("\n⏹️  自动套利扫描已停止")
        print("👋 程序已退出")

if __name__ == "__main__":
    main()
