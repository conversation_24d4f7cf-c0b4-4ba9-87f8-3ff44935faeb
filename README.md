# 多交易所实时价格监控系统

这是一个综合的加密货币实时价格监控系统，可以同时监控多个主流交易所的价格并进行价差分析。

## 功能特点

- **多交易所支持**: 同时监控 11 个主流交易所
  - Gemini (REST API)
  - Gate.io (WebSocket)
  - MEXC (REST API)
  - Bitfinex (WebSocket)
  - Bitstamp (WebSocket)
  - Binance (WebSocket) ✅
  - Coinbase (WebSocket) ⚠️
  - Kraken (WebSocket) ⚠️
  - KuCoin (WebSocket) ⚠️
  - Bybit (WebSocket) ✅
  - OKX (WebSocket) ✅

- **实时价格更新**: 使用 WebSocket 和 REST API 获取最新价格
- **价差分析**: 自动计算并显示最高价、最低价和价差
- **清晰的显示格式**: 按照你要求的表格格式显示数据

## 安装依赖

```bash
python3 -m pip install websocket-client pytz requests
```

## 使用方法

### 基本使用

```bash
python3 multi_exchange_price_monitor.py
```

### 自定义交易对

你可以修改代码中的参数来监控不同的交易对：

```python
# 在 __main__ 部分修改
monitor = MultiExchangePriceMonitor(symbol="BTC", base_currency="USDT")
```

支持的交易对示例：
- BTC/USDT
- ETH/USDT
- LTC/USD
- XRP/USD

## 显示格式

系统会按照以下格式显示数据：

```
====================================================================================================
实时价格监控 - BTC/USDT
更新时间: 2025-06-19 02:48:20
====================================================================================================
| MEXC         | 02:48:20 | BTCUSDT    | $ 104392.85 |
| OKX          | 02:48:20 | BTC-USDT   | $ 104402.00 |
| Bybit        | 02:48:19 | BTCUSDT    | $ 104403.80 |
| Gate.io      | 02:48:19 | BTC_USDT   | $ 104393.70 |
| Bitfinex     | 02:48:18 | BTCUSD     | $ 104560.00 |
| Gemini       | 02:48:20 | BTCUSD     | $ 104460.98 |
| Binance      | 02:48:20 | BTCUSDT    | $ 104394.90 |
| Bitstamp     | 02:48:16 | btcusd     | $ 104399.00 |
----------------------------------------------------------------------------------------------------
最高价格        | 最低价格          | 相差
Bitfinex: $104560.00    | MEXC: $104392.85 | $167.15
价差百分比: 0.160%
====================================================================================================
```

## 技术特点

### 连接方式
- **WebSocket**: 用于实时性要求高的交易所 (Gate.io, Bitfinex, Bitstamp)
- **REST API**: 用于稳定性要求高的交易所 (Gemini, MEXC)

### 错误处理
- 自动重连机制
- 网络异常处理
- 数据解析错误处理

### 性能优化
- 多线程并发获取数据
- 线程安全的价格存储
- 智能显示更新频率

## 停止程序

使用 `Ctrl+C` 来安全停止程序。

## 交易所连接状态

### ✅ 稳定连接
- **Binance**: 全球最大交易所，连接稳定
- **OKX**: 亚洲主流交易所，实时数据
- **Bybit**: 衍生品交易所，现货数据稳定
- **Gate.io**: 多币种支持，连接良好
- **MEXC**: REST API稳定
- **Gemini**: 美国合规交易所
- **Bitfinex**: 老牌交易所
- **Bitstamp**: 欧洲交易所

### ⚠️ 可能的连接问题
- **Coinbase**: 可能有地理位置限制 (HTTP 520)
- **KuCoin**: 可能需要特殊认证
- **Kraken**: 连接较慢，需要等待

## 注意事项

1. **网络要求**: 需要稳定的网络连接访问各交易所 API
2. **API 限制**: 各交易所可能有 API 调用频率限制
3. **时区设置**: 默认使用 Asia/Shanghai 时区，可在代码中修改
4. **SSL 警告**: 可能会看到 SSL 相关警告，这是正常的，不影响功能
5. **地理限制**: 某些交易所可能对特定地区有访问限制

## 自定义配置

### 修改更新频率

在 `display_prices` 方法中修改：
```python
time.sleep(3)  # 每3秒更新一次显示
```

### 修改轮询间隔

在 `start_rest_feed` 方法中修改：
```python
time.sleep(1)  # 1秒轮询一次
```

### 添加新的交易所

在 `exchanges` 字典中添加新的交易所配置，并实现相应的连接方法。

## 故障排除

### 常见问题

1. **连接失败**: 检查网络连接和防火墙设置
2. **数据不更新**: 可能是 API 限制，等待一段时间后重试
3. **价格异常**: 某些交易所可能暂时不可用，系统会自动处理

### 日志信息

程序会输出详细的日志信息，包括：
- 连接状态
- 错误信息
- 重连尝试

## 许可证

本项目仅供学习和研究使用。
