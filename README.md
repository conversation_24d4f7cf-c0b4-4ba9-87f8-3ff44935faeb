# 多交易所实时价格监控系统

这是一个综合的加密货币实时价格监控系统，可以同时监控多个主流交易所的价格并进行价差分析。

## 功能特点

- **多交易所支持**: 同时监控 5 个主流交易所
  - Gemini (REST API)
  - Gate.io (WebSocket)
  - MEXC (REST API)
  - Bitfinex (WebSocket)
  - Bitstamp (WebSocket)

- **实时价格更新**: 使用 WebSocket 和 REST API 获取最新价格
- **价差分析**: 自动计算并显示最高价、最低价和价差
- **清晰的显示格式**: 按照你要求的表格格式显示数据

## 安装依赖

```bash
python3 -m pip install websocket-client pytz requests
```

## 使用方法

### 基本使用

```bash
python3 multi_exchange_price_monitor.py
```

### 自定义交易对

你可以修改代码中的参数来监控不同的交易对：

```python
# 在 __main__ 部分修改
monitor = MultiExchangePriceMonitor(symbol="BTC", base_currency="USDT")
```

支持的交易对示例：
- BTC/USDT
- ETH/USDT
- LTC/USD
- XRP/USD

## 显示格式

系统会按照以下格式显示数据：

```
====================================================================================================
实时价格监控 - BTC/USDT
更新时间: 2025-06-19 02:36:11
====================================================================================================
| MEXC         | 02:36:10 | BTCUSDT    | $ 104260.52 |
| Gate.io      | 02:36:09 | BTC_USDT   | $ 104273.90 |
| Gemini       | 02:36:10 | BTCUSD     | $ 104312.65 |
| Bitfinex     | 02:36:03 | BTCUSD     | $ 104420.00 |
| Bitstamp     | 02:36:09 | btcusd     | $ 104307.00 |
----------------------------------------------------------------------------------------------------
最高价格        | 最低价格          | 相差
Bitfinex: $104420.00    | MEXC: $104260.52 | $159.48
价差百分比: 0.153%
====================================================================================================
```

## 技术特点

### 连接方式
- **WebSocket**: 用于实时性要求高的交易所 (Gate.io, Bitfinex, Bitstamp)
- **REST API**: 用于稳定性要求高的交易所 (Gemini, MEXC)

### 错误处理
- 自动重连机制
- 网络异常处理
- 数据解析错误处理

### 性能优化
- 多线程并发获取数据
- 线程安全的价格存储
- 智能显示更新频率

## 停止程序

使用 `Ctrl+C` 来安全停止程序。

## 注意事项

1. **网络要求**: 需要稳定的网络连接访问各交易所 API
2. **API 限制**: 各交易所可能有 API 调用频率限制
3. **时区设置**: 默认使用 Asia/Shanghai 时区，可在代码中修改
4. **SSL 警告**: 可能会看到 SSL 相关警告，这是正常的，不影响功能

## 自定义配置

### 修改更新频率

在 `display_prices` 方法中修改：
```python
time.sleep(3)  # 每3秒更新一次显示
```

### 修改轮询间隔

在 `start_rest_feed` 方法中修改：
```python
time.sleep(1)  # 1秒轮询一次
```

### 添加新的交易所

在 `exchanges` 字典中添加新的交易所配置，并实现相应的连接方法。

## 故障排除

### 常见问题

1. **连接失败**: 检查网络连接和防火墙设置
2. **数据不更新**: 可能是 API 限制，等待一段时间后重试
3. **价格异常**: 某些交易所可能暂时不可用，系统会自动处理

### 日志信息

程序会输出详细的日志信息，包括：
- 连接状态
- 错误信息
- 重连尝试

## 许可证

本项目仅供学习和研究使用。
