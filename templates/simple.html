<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 简化版价格监控</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { padding: 20px; background-color: #f8f9fa; }
        .status-card { margin: 20px 0; }
        .data-card { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 10px; background: white; }
        .arbitrage-item { margin: 10px 0; padding: 15px; background: #fff3cd; border-radius: 8px; border-left: 4px solid #ffc107; }
        .price-item { margin: 5px 0; padding: 8px; background: #f8f9fa; border-radius: 5px; font-family: monospace; }
        .loading { color: #666; font-style: italic; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; }
        .success { color: #155724; background: #d4edda; padding: 10px; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1><i class="fas fa-chart-line"></i> 简化版价格监控系统</h1>
        
        <!-- 控制面板 -->
        <div class="row status-card">
            <div class="col-md-8">
                <button id="startBtn" class="btn btn-success btn-lg me-3" onclick="startScan()">
                    <i class="fas fa-play"></i> 启动扫描
                </button>
                <button id="stopBtn" class="btn btn-danger btn-lg me-3" onclick="stopScan()" disabled>
                    <i class="fas fa-stop"></i> 停止扫描
                </button>
                <button class="btn btn-info btn-lg" onclick="testAPI()">
                    <i class="fas fa-test-tube"></i> 测试API
                </button>
            </div>
            <div class="col-md-4">
                <div id="status" class="alert alert-secondary">
                    <i class="fas fa-circle text-secondary"></i> 未启动
                </div>
            </div>
        </div>

        <!-- 统计面板 -->
        <div class="row">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h5><i class="fas fa-coins"></i> 监控币种</h5>
                        <h3 id="cryptoCount">0</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h5><i class="fas fa-exchange-alt"></i> 交易所</h5>
                        <h3 id="exchangeCount">0</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body text-center">
                        <h5><i class="fas fa-percentage"></i> 最大套利</h5>
                        <h3 id="maxArbitrage">0.00%</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <h5><i class="fas fa-clock"></i> 最后更新</h5>
                        <h6 id="lastUpdate">--:--:--</h6>
                    </div>
                </div>
            </div>
        </div>

        <!-- 最佳套利机会 -->
        <div class="data-card">
            <h3><i class="fas fa-trophy"></i> 🏆 最佳套利机会</h3>
            <div id="bestArbitrage" class="loading">等待扫描数据...</div>
        </div>

        <!-- 套利排行榜 -->
        <div class="data-card">
            <h3><i class="fas fa-list-ol"></i> 套利机会排行榜</h3>
            <div id="arbitrageList" class="loading">等待扫描数据...</div>
        </div>

        <!-- 实时价格 -->
        <div class="data-card">
            <h3><i class="fas fa-table"></i> 实时价格监控</h3>
            <div id="priceTable" class="loading">等待价格数据...</div>
        </div>

        <!-- 日志 -->
        <div class="data-card">
            <h3><i class="fas fa-terminal"></i> 系统日志</h3>
            <div id="logArea" style="max-height: 200px; overflow-y: auto; background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 12px;"></div>
        </div>
    </div>

    <script>
        let isScanning = false;
        let updateInterval;

        // 日志函数
        function log(message) {
            const logArea = document.getElementById('logArea');
            const time = new Date().toLocaleTimeString();
            const logEntry = `[${time}] ${message}`;
            logArea.innerHTML = `<div>${logEntry}</div>` + logArea.innerHTML;
            console.log(logEntry);
        }

        // 更新状态
        function updateStatus(text, type = 'secondary', icon = 'circle') {
            const statusDiv = document.getElementById('status');
            const iconClass = type === 'success' ? 'text-success' : type === 'danger' ? 'text-danger' : type === 'warning' ? 'text-warning' : 'text-secondary';
            statusDiv.innerHTML = `<i class="fas fa-${icon} ${iconClass}"></i> ${text}`;
            statusDiv.className = `alert alert-${type}`;
        }

        // 启动扫描
        async function startScan() {
            try {
                log('🚀 用户点击启动扫描按钮');
                updateStatus('正在启动...', 'warning', 'spinner fa-spin');
                
                const response = await fetch('/api/start_scan');
                log(`📡 API响应状态: ${response.status}`);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }
                
                const result = await response.json();
                log(`📊 API响应数据: ${JSON.stringify(result)}`);
                
                if (result.status === 'started' || result.status === 'already_running') {
                    isScanning = true;
                    updateStatus('扫描中', 'success', 'play');
                    document.getElementById('startBtn').disabled = true;
                    document.getElementById('stopBtn').disabled = false;
                    
                    log('✅ 扫描启动成功，开始数据更新');
                    
                    // 立即加载数据并开始定期更新
                    loadData();
                    updateInterval = setInterval(loadData, 5000);
                } else {
                    throw new Error('启动失败: ' + result.status);
                }
            } catch (error) {
                log(`❌ 启动扫描失败: ${error.message}`);
                updateStatus('启动失败', 'danger', 'exclamation-triangle');
                alert('启动失败: ' + error.message);
            }
        }

        // 停止扫描
        async function stopScan() {
            try {
                log('⏹️ 用户点击停止扫描按钮');
                
                const response = await fetch('/api/stop_scan');
                const result = await response.json();
                
                if (result.status === 'stopped') {
                    isScanning = false;
                    updateStatus('已停止', 'secondary', 'stop');
                    document.getElementById('startBtn').disabled = false;
                    document.getElementById('stopBtn').disabled = true;
                    
                    if (updateInterval) {
                        clearInterval(updateInterval);
                    }
                    
                    log('✅ 扫描已停止');
                }
            } catch (error) {
                log(`❌ 停止扫描失败: ${error.message}`);
            }
        }

        // 测试API
        async function testAPI() {
            try {
                log('🧪 测试API连接');
                const response = await fetch('/api/current_data');
                const data = await response.json();
                
                log(`✅ API测试成功，获取到 ${Object.keys(data.prices || {}).length} 个币种数据`);
                
                if (data.prices || data.arbitrage) {
                    updateData(data);
                }
            } catch (error) {
                log(`❌ API测试失败: ${error.message}`);
            }
        }

        // 加载数据
        async function loadData() {
            try {
                const response = await fetch('/api/current_data');
                const data = await response.json();
                
                if (data && (data.prices || data.arbitrage)) {
                    updateData(data);
                    log(`🔄 数据更新成功 - ${Object.keys(data.prices || {}).length} 个币种`);
                } else {
                    log('⚠️ 暂无数据');
                }
            } catch (error) {
                log(`❌ 数据加载失败: ${error.message}`);
            }
        }

        // 更新数据
        function updateData(data) {
            // 更新统计
            const cryptoCount = Object.keys(data.prices || {}).length;
            const exchangeCount = getUniqueExchangeCount(data.prices);
            const maxArbitrage = getMaxArbitragePercentage(data.arbitrage);

            document.getElementById('cryptoCount').textContent = cryptoCount;
            document.getElementById('exchangeCount').textContent = exchangeCount;
            document.getElementById('maxArbitrage').textContent = maxArbitrage + '%';

            // 更新时间
            if (data.timestamp) {
                const date = new Date(data.timestamp);
                document.getElementById('lastUpdate').textContent = date.toLocaleTimeString('zh-CN');
            }

            // 更新最佳套利机会
            updateBestArbitrage(data.arbitrage);
            
            // 更新套利列表
            updateArbitrageList(data.arbitrage);
            
            // 更新价格表
            updatePriceTable(data.prices);
        }

        // 更新最佳套利机会
        function updateBestArbitrage(arbitrageData) {
            const div = document.getElementById('bestArbitrage');
            
            if (!arbitrageData || Object.keys(arbitrageData).length === 0) {
                div.innerHTML = '<div class="loading">暂无套利数据</div>';
                return;
            }

            const sorted = Object.entries(arbitrageData)
                .sort((a, b) => b[1].arbitrage.percentage_diff - a[1].arbitrage.percentage_diff);

            if (sorted.length > 0) {
                const [crypto, data] = sorted[0];
                const arb = data.arbitrage;
                
                div.innerHTML = `
                    <div style="background: linear-gradient(135deg, #ffc107, #ff8c00); color: white; border-radius: 15px; padding: 20px;">
                        <h4><i class="fas fa-trophy"></i> ${data.info.name} (${crypto})</h4>
                        <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 15px; margin-top: 15px;">
                            <div style="text-align: center; background: rgba(255,255,255,0.2); padding: 10px; border-radius: 8px;">
                                <h5>${arb.percentage_diff.toFixed(3)}%</h5>
                                <small>套利空间</small>
                            </div>
                            <div style="text-align: center; background: rgba(255,255,255,0.2); padding: 10px; border-radius: 8px;">
                                <h5>$${arb.min_price.toFixed(4)}</h5>
                                <small>买入: ${arb.min_exchange}</small>
                            </div>
                            <div style="text-align: center; background: rgba(255,255,255,0.2); padding: 10px; border-radius: 8px;">
                                <h5>$${arb.max_price.toFixed(4)}</h5>
                                <small>卖出: ${arb.max_exchange}</small>
                            </div>
                            <div style="text-align: center; background: rgba(255,255,255,0.2); padding: 10px; border-radius: 8px;">
                                <h5>$${arb.price_diff.toFixed(4)}</h5>
                                <small>价差利润</small>
                            </div>
                        </div>
                        <div style="margin-top: 15px; text-align: center; font-weight: bold;">
                            最高价格: ${arb.max_exchange} $${arb.max_price.toFixed(4)} | 最低价格: ${arb.min_exchange} $${arb.min_price.toFixed(4)} | 相差: $${arb.price_diff.toFixed(4)}
                        </div>
                    </div>
                `;
            }
        }

        // 更新套利列表
        function updateArbitrageList(arbitrageData) {
            const div = document.getElementById('arbitrageList');
            
            if (!arbitrageData || Object.keys(arbitrageData).length === 0) {
                div.innerHTML = '<div class="loading">暂无套利数据</div>';
                return;
            }

            const sorted = Object.entries(arbitrageData)
                .sort((a, b) => b[1].arbitrage.percentage_diff - a[1].arbitrage.percentage_diff);

            let html = '';
            sorted.forEach(([crypto, data], index) => {
                const arb = data.arbitrage;
                const rank = index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : `${index + 1}.`;
                
                html += `
                    <div class="arbitrage-item">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                                <strong>${rank} ${data.info.name} (${crypto})</strong><br>
                                <small>套利空间: <span style="color: #e67e22; font-weight: bold;">${arb.percentage_diff.toFixed(3)}%</span></small>
                            </div>
                            <div style="text-align: right;">
                                <div>买入: ${arb.min_exchange} $${arb.min_price.toFixed(4)}</div>
                                <div>卖出: ${arb.max_exchange} $${arb.max_price.toFixed(4)}</div>
                                <div>利润: $${arb.price_diff.toFixed(4)}</div>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            div.innerHTML = html;
        }

        // 更新价格表
        function updatePriceTable(pricesData) {
            const div = document.getElementById('priceTable');
            
            if (!pricesData || Object.keys(pricesData).length === 0) {
                div.innerHTML = '<div class="loading">暂无价格数据</div>';
                return;
            }

            let html = '';
            Object.entries(pricesData).forEach(([crypto, prices]) => {
                html += `<h5>${crypto}</h5>`;
                Object.entries(prices).forEach(([exchange, data]) => {
                    html += `
                        <div class="price-item">
                            <strong>${exchange}:</strong> $${data.price.toFixed(4)}
                        </div>
                    `;
                });
                html += '<hr>';
            });
            
            div.innerHTML = html;
        }

        // 辅助函数
        function getUniqueExchangeCount(pricesData) {
            const exchanges = new Set();
            Object.values(pricesData || {}).forEach(cryptoPrices => {
                Object.keys(cryptoPrices).forEach(exchange => exchanges.add(exchange));
            });
            return exchanges.size;
        }

        function getMaxArbitragePercentage(arbitrageData) {
            if (!arbitrageData || Object.keys(arbitrageData).length === 0) {
                return '0.00';
            }
            
            const maxPercentage = Math.max(
                ...Object.values(arbitrageData).map(data => data.arbitrage.percentage_diff)
            );
            
            return maxPercentage.toFixed(3);
        }

        // 页面加载完成
        window.onload = function() {
            log('📱 页面加载完成');
            updateStatus('未启动', 'secondary', 'circle');
            
            // 测试按钮是否正常
            log('🔧 按钮功能测试完成');
        };
    </script>
</body>
</html>
