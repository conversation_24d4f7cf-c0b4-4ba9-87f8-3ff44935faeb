<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="theme-color" content="#007bff">
    <title>🚀 加密货币套利监控</title>
    
    <!-- PWA Manifest -->
    <link rel="manifest" href="/static/manifest.json">
    
    <!-- 移动端优化的CSS -->
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            overflow-x: hidden;
        }
        
        .container {
            padding: 10px;
            max-width: 100vw;
        }
        
        .header {
            text-align: center;
            padding: 20px 0;
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            margin-bottom: 15px;
            backdrop-filter: blur(10px);
        }
        
        .header h1 {
            font-size: 1.5rem;
            margin-bottom: 10px;
        }
        
        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            justify-content: center;
        }
        
        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 25px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
            min-width: 100px;
        }
        
        .btn-primary {
            background: #28a745;
            color: white;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        
        .status {
            text-align: center;
            padding: 10px;
            border-radius: 10px;
            margin-bottom: 15px;
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
        }
        
        .card {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 15px;
            margin-bottom: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .card h3 {
            margin-bottom: 15px;
            font-size: 1.2rem;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .best-arbitrage {
            background: linear-gradient(135deg, #ffc107, #ff8c00);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
        }
        
        .arbitrage-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            margin: 15px 0;
        }
        
        .arbitrage-item {
            background: rgba(255,255,255,0.2);
            padding: 10px;
            border-radius: 10px;
            text-align: center;
        }
        
        .arbitrage-item h4 {
            font-size: 1.1rem;
            margin-bottom: 5px;
        }
        
        .arbitrage-item small {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        
        .arbitrage-list {
            max-height: 300px;
            overflow-y: auto;
        }
        
        .arbitrage-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            margin-bottom: 8px;
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            border-left: 4px solid #ffc107;
        }
        
        .arbitrage-info {
            flex: 1;
        }
        
        .arbitrage-profit {
            text-align: right;
            font-weight: bold;
        }
        
        .price-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 8px;
        }
        
        .price-item {
            background: rgba(255,255,255,0.1);
            padding: 8px;
            border-radius: 8px;
            text-align: center;
            font-family: monospace;
            font-size: 0.9rem;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            opacity: 0.7;
        }
        
        .spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .stat-card {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }
        
        .stat-value {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.8;
        }
        
        /* 响应式设计 */
        @media (max-width: 480px) {
            .arbitrage-grid {
                grid-template-columns: 1fr;
            }
            
            .controls {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 200px;
            }
        }
        
        /* 滚动条样式 */
        ::-webkit-scrollbar {
            width: 6px;
        }
        
        ::-webkit-scrollbar-track {
            background: rgba(255,255,255,0.1);
            border-radius: 3px;
        }
        
        ::-webkit-scrollbar-thumb {
            background: rgba(255,255,255,0.3);
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <h1>🚀 加密货币套利监控</h1>
            <div id="status" class="status">未启动</div>
        </div>

        <!-- 控制按钮 -->
        <div class="controls">
            <button id="startBtn" class="btn btn-primary" onclick="startScan()">
                ▶️ 启动扫描
            </button>
            <button id="stopBtn" class="btn btn-danger" onclick="stopScan()" disabled>
                ⏹️ 停止扫描
            </button>
        </div>

        <!-- 统计信息 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div id="cryptoCount" class="stat-value">0</div>
                <div class="stat-label">监控币种</div>
            </div>
            <div class="stat-card">
                <div id="maxArbitrage" class="stat-value">0.00%</div>
                <div class="stat-label">最大套利</div>
            </div>
        </div>

        <!-- 最佳套利机会 -->
        <div class="card">
            <h3>🏆 最佳套利机会</h3>
            <div id="bestArbitrage" class="loading">
                <div class="spinner"></div>
                <div>等待扫描数据...</div>
            </div>
        </div>

        <!-- 套利排行榜 -->
        <div class="card">
            <h3>📊 套利排行榜</h3>
            <div id="arbitrageList" class="arbitrage-list loading">
                <div class="spinner"></div>
                <div>等待扫描数据...</div>
            </div>
        </div>

        <!-- 实时价格 -->
        <div class="card">
            <h3>💱 实时价格</h3>
            <div id="priceGrid" class="loading">
                <div class="spinner"></div>
                <div>等待价格数据...</div>
            </div>
        </div>
    </div>

    <script>
        let isScanning = false;
        let updateInterval;

        // 启动扫描
        async function startScan() {
            try {
                updateStatus('正在启动...', '#ffc107');
                
                const response = await fetch('/api/start_scan');
                const result = await response.json();
                
                if (result.status === 'started' || result.status === 'already_running') {
                    isScanning = true;
                    updateStatus('扫描中', '#28a745');
                    document.getElementById('startBtn').disabled = true;
                    document.getElementById('stopBtn').disabled = false;
                    
                    // 立即加载数据并开始定期更新
                    loadData();
                    updateInterval = setInterval(loadData, 5000);
                } else {
                    throw new Error('启动失败');
                }
            } catch (error) {
                updateStatus('启动失败', '#dc3545');
                alert('启动失败: ' + error.message);
            }
        }

        // 停止扫描
        async function stopScan() {
            try {
                const response = await fetch('/api/stop_scan');
                const result = await response.json();
                
                if (result.status === 'stopped') {
                    isScanning = false;
                    updateStatus('已停止', '#6c757d');
                    document.getElementById('startBtn').disabled = false;
                    document.getElementById('stopBtn').disabled = true;
                    
                    if (updateInterval) {
                        clearInterval(updateInterval);
                    }
                }
            } catch (error) {
                alert('停止失败: ' + error.message);
            }
        }

        // 更新状态
        function updateStatus(text, color) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = text;
            statusDiv.style.background = color;
        }

        // 加载数据
        async function loadData() {
            try {
                const response = await fetch('/api/current_data');
                const data = await response.json();
                
                if (data && (data.prices || data.arbitrage)) {
                    updateData(data);
                }
            } catch (error) {
                console.error('数据加载失败:', error);
            }
        }

        // 更新数据
        function updateData(data) {
            // 更新统计
            const cryptoCount = Object.keys(data.prices || {}).length;
            const maxArbitrage = getMaxArbitragePercentage(data.arbitrage);

            document.getElementById('cryptoCount').textContent = cryptoCount;
            document.getElementById('maxArbitrage').textContent = maxArbitrage + '%';

            // 更新最佳套利机会
            updateBestArbitrage(data.arbitrage);
            
            // 更新套利列表
            updateArbitrageList(data.arbitrage);
            
            // 更新价格网格
            updatePriceGrid(data.prices);
        }

        // 更新最佳套利机会
        function updateBestArbitrage(arbitrageData) {
            const div = document.getElementById('bestArbitrage');
            
            if (!arbitrageData || Object.keys(arbitrageData).length === 0) {
                div.innerHTML = '<div class="loading"><div class="spinner"></div><div>暂无套利数据</div></div>';
                return;
            }

            const sorted = Object.entries(arbitrageData)
                .sort((a, b) => b[1].arbitrage.percentage_diff - a[1].arbitrage.percentage_diff);

            if (sorted.length > 0) {
                const [crypto, data] = sorted[0];
                const arb = data.arbitrage;
                
                div.innerHTML = `
                    <div class="best-arbitrage">
                        <h4>🏆 ${data.info.name} (${crypto})</h4>
                        <div class="arbitrage-grid">
                            <div class="arbitrage-item">
                                <h4>${arb.percentage_diff.toFixed(3)}%</h4>
                                <small>套利空间</small>
                            </div>
                            <div class="arbitrage-item">
                                <h4>$${arb.price_diff.toFixed(4)}</h4>
                                <small>价差利润</small>
                            </div>
                        </div>
                        <div style="font-size: 0.9rem; margin-top: 10px;">
                            <strong>买入:</strong> ${arb.min_exchange} $${arb.min_price.toFixed(4)}<br>
                            <strong>卖出:</strong> ${arb.max_exchange} $${arb.max_price.toFixed(4)}
                        </div>
                    </div>
                `;
            }
        }

        // 更新套利列表
        function updateArbitrageList(arbitrageData) {
            const div = document.getElementById('arbitrageList');
            
            if (!arbitrageData || Object.keys(arbitrageData).length === 0) {
                div.innerHTML = '<div class="loading"><div class="spinner"></div><div>暂无套利数据</div></div>';
                return;
            }

            const sorted = Object.entries(arbitrageData)
                .sort((a, b) => b[1].arbitrage.percentage_diff - a[1].arbitrage.percentage_diff);

            let html = '';
            sorted.slice(0, 5).forEach(([crypto, data], index) => {
                const arb = data.arbitrage;
                const rank = index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : `${index + 1}.`;
                
                html += `
                    <div class="arbitrage-row">
                        <div class="arbitrage-info">
                            <div><strong>${rank} ${data.info.name}</strong></div>
                            <div style="font-size: 0.8rem; opacity: 0.8;">${crypto}</div>
                        </div>
                        <div class="arbitrage-profit">
                            <div style="color: #ffc107; font-size: 1.1rem;">${arb.percentage_diff.toFixed(3)}%</div>
                            <div style="font-size: 0.8rem;">$${arb.price_diff.toFixed(4)}</div>
                        </div>
                    </div>
                `;
            });
            
            div.innerHTML = html;
        }

        // 更新价格网格
        function updatePriceGrid(pricesData) {
            const div = document.getElementById('priceGrid');
            
            if (!pricesData || Object.keys(pricesData).length === 0) {
                div.innerHTML = '<div class="loading"><div class="spinner"></div><div>暂无价格数据</div></div>';
                return;
            }

            let html = '<div class="price-grid">';
            Object.entries(pricesData).forEach(([crypto, prices]) => {
                const priceCount = Object.keys(prices).length;
                html += `
                    <div class="price-item">
                        <div style="font-weight: bold; margin-bottom: 5px;">${crypto}</div>
                        <div style="font-size: 0.8rem; opacity: 0.8;">${priceCount} 个交易所</div>
                    </div>
                `;
            });
            html += '</div>';
            
            div.innerHTML = html;
        }

        // 获取最大套利百分比
        function getMaxArbitragePercentage(arbitrageData) {
            if (!arbitrageData || Object.keys(arbitrageData).length === 0) {
                return '0.00';
            }
            
            const maxPercentage = Math.max(
                ...Object.values(arbitrageData).map(data => data.arbitrage.percentage_diff)
            );
            
            return maxPercentage.toFixed(3);
        }

        // 页面加载完成
        window.addEventListener('load', () => {
            // 尝试加载初始数据
            loadData();
        });

        // PWA安装提示
        let deferredPrompt;
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            deferredPrompt = e;
            
            // 显示安装提示
            const installBtn = document.createElement('button');
            installBtn.textContent = '📱 安装应用';
            installBtn.className = 'btn btn-primary';
            installBtn.style.position = 'fixed';
            installBtn.style.bottom = '20px';
            installBtn.style.right = '20px';
            installBtn.style.zIndex = '1000';
            
            installBtn.addEventListener('click', () => {
                deferredPrompt.prompt();
                deferredPrompt.userChoice.then((choiceResult) => {
                    if (choiceResult.outcome === 'accepted') {
                        console.log('用户安装了PWA');
                    }
                    deferredPrompt = null;
                    installBtn.remove();
                });
            });
            
            document.body.appendChild(installBtn);
        });
    </script>
</body>
</html>
