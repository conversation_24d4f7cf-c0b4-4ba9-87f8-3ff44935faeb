<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 数据测试页面</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { padding: 20px; }
        .data-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .price-item { margin: 5px 0; padding: 5px; background: #f8f9fa; border-radius: 3px; }
        .arbitrage-item { margin: 10px 0; padding: 10px; background: #fff3cd; border-radius: 5px; }
        .loading { color: #666; font-style: italic; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; }
        .success { color: #155724; background: #d4edda; padding: 10px; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 数据测试页面</h1>
        
        <div class="row">
            <div class="col-md-6">
                <button id="testBtn" class="btn btn-primary">测试数据获取</button>
                <button id="startBtn" class="btn btn-success">启动扫描</button>
                <button id="stopBtn" class="btn btn-danger">停止扫描</button>
            </div>
            <div class="col-md-6">
                <div id="status" class="alert alert-secondary">未开始</div>
            </div>
        </div>

        <div class="data-section">
            <h3>📊 API测试结果</h3>
            <div id="apiResult" class="loading">点击"测试数据获取"按钮</div>
        </div>

        <div class="data-section">
            <h3>💰 套利机会</h3>
            <div id="arbitrageResult" class="loading">等待数据...</div>
        </div>

        <div class="data-section">
            <h3>💱 价格数据</h3>
            <div id="priceResult" class="loading">等待数据...</div>
        </div>

        <div class="data-section">
            <h3>🔄 实时更新</h3>
            <div id="updateLog"></div>
        </div>
    </div>

    <script>
        let updateInterval;
        
        function log(message) {
            const logDiv = document.getElementById('updateLog');
            const time = new Date().toLocaleTimeString();
            logDiv.innerHTML = `<div>[${time}] ${message}</div>` + logDiv.innerHTML;
            console.log(`[${time}] ${message}`);
        }

        async function testData() {
            document.getElementById('apiResult').innerHTML = '<div class="loading">正在获取数据...</div>';
            
            try {
                const response = await fetch('/api/current_data');
                const data = await response.json();
                
                log('✅ 数据获取成功');
                
                // 显示原始数据
                document.getElementById('apiResult').innerHTML = `
                    <div class="success">
                        <strong>数据获取成功！</strong><br>
                        币种数量: ${Object.keys(data.prices || {}).length}<br>
                        套利机会: ${Object.keys(data.arbitrage || {}).length}<br>
                        更新时间: ${data.timestamp || '未知'}
                    </div>
                    <details>
                        <summary>查看原始数据</summary>
                        <pre style="max-height: 300px; overflow-y: auto; font-size: 12px;">${JSON.stringify(data, null, 2)}</pre>
                    </details>
                `;
                
                // 显示套利机会
                displayArbitrage(data.arbitrage);
                
                // 显示价格数据
                displayPrices(data.prices);
                
            } catch (error) {
                log('❌ 数据获取失败: ' + error.message);
                document.getElementById('apiResult').innerHTML = `
                    <div class="error">
                        <strong>数据获取失败！</strong><br>
                        错误: ${error.message}
                    </div>
                `;
            }
        }

        function displayArbitrage(arbitrageData) {
            const div = document.getElementById('arbitrageResult');
            
            if (!arbitrageData || Object.keys(arbitrageData).length === 0) {
                div.innerHTML = '<div class="loading">暂无套利数据</div>';
                return;
            }
            
            const sorted = Object.entries(arbitrageData)
                .sort((a, b) => b[1].arbitrage.percentage_diff - a[1].arbitrage.percentage_diff);
            
            let html = '';
            sorted.forEach(([crypto, data], index) => {
                const arb = data.arbitrage;
                const rank = index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : `${index + 1}.`;
                
                html += `
                    <div class="arbitrage-item">
                        <strong>${rank} ${data.info.name} (${crypto})</strong><br>
                        套利空间: <span style="color: #e67e22; font-weight: bold;">${arb.percentage_diff.toFixed(3)}%</span><br>
                        买入: ${arb.min_exchange} $${arb.min_price.toFixed(4)}<br>
                        卖出: ${arb.max_exchange} $${arb.max_price.toFixed(4)}<br>
                        价差: $${arb.price_diff.toFixed(4)}
                    </div>
                `;
            });
            
            div.innerHTML = html;
        }

        function displayPrices(pricesData) {
            const div = document.getElementById('priceResult');
            
            if (!pricesData || Object.keys(pricesData).length === 0) {
                div.innerHTML = '<div class="loading">暂无价格数据</div>';
                return;
            }
            
            let html = '';
            Object.entries(pricesData).forEach(([crypto, prices]) => {
                html += `<h5>${crypto}</h5>`;
                Object.entries(prices).forEach(([exchange, data]) => {
                    html += `
                        <div class="price-item">
                            <strong>${exchange}:</strong> $${data.price.toFixed(4)}
                        </div>
                    `;
                });
            });
            
            div.innerHTML = html;
        }

        async function startScan() {
            try {
                const response = await fetch('/api/start_scan');
                const result = await response.json();
                
                if (result.status === 'started' || result.status === 'already_running') {
                    document.getElementById('status').innerHTML = '<div class="alert alert-success">扫描已启动</div>';
                    log('🚀 扫描已启动');
                    
                    // 开始定期更新
                    updateInterval = setInterval(testData, 3000);
                }
            } catch (error) {
                log('❌ 启动失败: ' + error.message);
                document.getElementById('status').innerHTML = '<div class="alert alert-danger">启动失败</div>';
            }
        }

        async function stopScan() {
            try {
                const response = await fetch('/api/stop_scan');
                const result = await response.json();
                
                if (result.status === 'stopped') {
                    document.getElementById('status').innerHTML = '<div class="alert alert-secondary">扫描已停止</div>';
                    log('⏹️ 扫描已停止');
                    
                    // 停止定期更新
                    if (updateInterval) {
                        clearInterval(updateInterval);
                    }
                }
            } catch (error) {
                log('❌ 停止失败: ' + error.message);
            }
        }

        // 绑定事件
        document.getElementById('testBtn').addEventListener('click', testData);
        document.getElementById('startBtn').addEventListener('click', startScan);
        document.getElementById('stopBtn').addEventListener('click', stopScan);

        // 页面加载时自动测试一次
        window.addEventListener('load', () => {
            log('📱 页面加载完成');
            testData();
        });
    </script>
</body>
</html>
