<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 多交易所价格监控系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <!-- 头部导航 -->
        <nav class="navbar navbar-dark bg-dark mb-4">
            <div class="container-fluid">
                <span class="navbar-brand mb-0 h1">
                    <i class="fas fa-chart-line"></i> 多交易所价格监控系统
                </span>
                <div class="d-flex">
                    <button id="startBtn" class="btn btn-success me-2">
                        <i class="fas fa-play"></i> 启动扫描
                    </button>
                    <button id="stopBtn" class="btn btn-danger me-2" disabled>
                        <i class="fas fa-stop"></i> 停止扫描
                    </button>
                    <span id="status" class="badge bg-secondary">未启动</span>
                </div>
            </div>
        </nav>

        <!-- 状态面板 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <h5 class="card-title"><i class="fas fa-coins"></i> 监控币种</h5>
                        <h3 id="cryptoCount">10</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <h5 class="card-title"><i class="fas fa-exchange-alt"></i> 交易所</h5>
                        <h3 id="exchangeCount">9</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <h5 class="card-title"><i class="fas fa-percentage"></i> 最大套利</h5>
                        <h3 id="maxArbitrage">0.00%</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <h5 class="card-title"><i class="fas fa-clock"></i> 最后更新</h5>
                        <h6 id="lastUpdate">--:--:--</h6>
                    </div>
                </div>
            </div>
        </div>

        <!-- 最佳套利机会 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-warning">
                        <h5 class="mb-0"><i class="fas fa-trophy"></i> 🏆 最佳套利机会</h5>
                    </div>
                    <div class="card-body">
                        <div id="bestArbitrage" class="alert alert-info">
                            <h4>等待扫描数据...</h4>
                            <p>点击"启动扫描"开始监控套利机会</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 套利排行榜 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0"><i class="fas fa-list-ol"></i> 套利机会排行榜</h5>
                    </div>
                    <div class="card-body">
                        <div id="arbitrageRanking" class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>排名</th>
                                        <th>币种</th>
                                        <th>套利空间</th>
                                        <th>买入交易所</th>
                                        <th>卖出交易所</th>
                                        <th>价差</th>
                                        <th>数据源</th>
                                    </tr>
                                </thead>
                                <tbody id="rankingTableBody">
                                    <tr>
                                        <td colspan="7" class="text-center">等待扫描数据...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 实时价格表格 -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-table"></i> 实时价格监控</h5>
                    </div>
                    <div class="card-body">
                        <div id="priceTable" class="table-responsive">
                            <!-- 价格表格将通过JavaScript动态生成 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 脚本 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
</body>
</html>
