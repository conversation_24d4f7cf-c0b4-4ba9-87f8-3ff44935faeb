<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 多交易所价格监控系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <!-- 头部导航 -->
        <nav class="navbar navbar-dark bg-dark mb-4">
            <div class="container-fluid">
                <span class="navbar-brand mb-0 h1">
                    <i class="fas fa-chart-line"></i> 多交易所价格监控系统
                </span>
                <div class="d-flex">
                    <button id="startBtn" class="btn btn-success me-2">
                        <i class="fas fa-play"></i> 启动扫描
                    </button>
                    <button id="stopBtn" class="btn btn-danger me-2" disabled>
                        <i class="fas fa-stop"></i> 停止扫描
                    </button>
                    <span id="status" class="badge bg-secondary">未启动</span>
                </div>
            </div>
        </nav>

        <!-- 状态面板 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <h5 class="card-title"><i class="fas fa-coins"></i> 监控币种</h5>
                        <h3 id="cryptoCount">10</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <h5 class="card-title"><i class="fas fa-exchange-alt"></i> 交易所</h5>
                        <h3 id="exchangeCount">9</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <h5 class="card-title"><i class="fas fa-percentage"></i> 最大套利</h5>
                        <h3 id="maxArbitrage">0.00%</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <h5 class="card-title"><i class="fas fa-clock"></i> 最后更新</h5>
                        <h6 id="lastUpdate">--:--:--</h6>
                    </div>
                </div>
            </div>
        </div>

        <!-- 最佳套利机会 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-warning">
                        <h5 class="mb-0"><i class="fas fa-trophy"></i> 🏆 最佳套利机会</h5>
                    </div>
                    <div class="card-body">
                        <div id="bestArbitrage" class="alert alert-info">
                            <h4>等待扫描数据...</h4>
                            <p>点击"启动扫描"开始监控套利机会</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 套利排行榜 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0"><i class="fas fa-list-ol"></i> 套利机会排行榜</h5>
                    </div>
                    <div class="card-body">
                        <div id="arbitrageRanking" class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>排名</th>
                                        <th>币种</th>
                                        <th>套利空间</th>
                                        <th>买入交易所</th>
                                        <th>卖出交易所</th>
                                        <th>价差</th>
                                        <th>数据源</th>
                                    </tr>
                                </thead>
                                <tbody id="rankingTableBody">
                                    <tr>
                                        <td colspan="7" class="text-center">等待扫描数据...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 实时价格表格 -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-table"></i> 实时价格监控</h5>
                    </div>
                    <div class="card-body">
                        <div id="priceTable" class="table-responsive">
                            <!-- 价格表格将通过JavaScript动态生成 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 脚本 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <script>
        // 简化版JavaScript - 直接嵌入页面
        let isScanning = false;
        let currentData = {};

        // 启动扫描
        async function startScanning() {
            try {
                console.log('🚀 Starting scan...');
                const response = await fetch('/api/start_scan');
                const result = await response.json();
                console.log('📡 Start scan response:', result);

                if (result.status === 'started' || result.status === 'already_running') {
                    isScanning = true;
                    updateStatus('扫描中', 'success');
                    document.getElementById('startBtn').disabled = true;
                    document.getElementById('stopBtn').disabled = false;

                    // 立即加载数据并开始定期更新
                    loadCurrentData();
                    setInterval(loadCurrentData, 5000);
                }
            } catch (error) {
                console.error('❌ Error starting scan:', error);
                updateStatus('启动失败', 'danger');
            }
        }

        // 停止扫描
        async function stopScanning() {
            try {
                const response = await fetch('/api/stop_scan');
                const result = await response.json();

                if (result.status === 'stopped') {
                    isScanning = false;
                    updateStatus('已停止', 'secondary');
                    document.getElementById('startBtn').disabled = false;
                    document.getElementById('stopBtn').disabled = true;
                }
            } catch (error) {
                console.error('Error stopping scan:', error);
            }
        }

        // 加载当前数据
        async function loadCurrentData() {
            try {
                console.log('🔄 Loading current data...');
                const response = await fetch('/api/current_data');
                const data = await response.json();
                console.log('📊 Received data:', data);

                if (data && (data.prices || data.arbitrage)) {
                    updateData(data);
                } else {
                    console.log('⚠️ No data available yet');
                }
            } catch (error) {
                console.error('❌ Error loading current data:', error);
            }
        }

        // 更新状态
        function updateStatus(text, type) {
            const statusElement = document.getElementById('status');
            statusElement.textContent = text;
            statusElement.className = `badge bg-${type}`;
        }

        // 更新数据
        function updateData(data) {
            console.log('🔄 Updating data:', data);
            currentData = data;
            updateStatistics(data);
            updateBestArbitrage(data.arbitrage);
            updateArbitrageRanking(data.arbitrage);
            updatePriceTable(data.prices);
            updateLastUpdate(data.timestamp);
            console.log('✅ Data update complete');
        }

        // 更新统计信息
        function updateStatistics(data) {
            const cryptoCount = Object.keys(data.prices || {}).length;
            const exchangeCount = getUniqueExchangeCount(data.prices);
            const maxArbitrage = getMaxArbitragePercentage(data.arbitrage);

            document.getElementById('cryptoCount').textContent = cryptoCount;
            document.getElementById('exchangeCount').textContent = exchangeCount;
            document.getElementById('maxArbitrage').textContent = maxArbitrage + '%';
        }

        // 更新最佳套利机会
        function updateBestArbitrage(arbitrageData) {
            const bestArbitrageDiv = document.getElementById('bestArbitrage');

            if (!arbitrageData || Object.keys(arbitrageData).length === 0) {
                bestArbitrageDiv.innerHTML = `
                    <div class="alert alert-info">
                        <h4><i class="fas fa-search"></i> 等待扫描数据...</h4>
                        <p>点击"启动扫描"开始监控套利机会</p>
                    </div>
                `;
                return;
            }

            // 找出最佳套利机会
            const sortedOpportunities = Object.entries(arbitrageData)
                .sort((a, b) => b[1].arbitrage.percentage_diff - a[1].arbitrage.percentage_diff);

            if (sortedOpportunities.length > 0) {
                const [crypto, data] = sortedOpportunities[0];
                const arb = data.arbitrage;

                bestArbitrageDiv.innerHTML = `
                    <div style="background: linear-gradient(135deg, #ffc107, #ff8c00); color: white; border-radius: 15px; padding: 20px;">
                        <h3><i class="fas fa-trophy"></i> ${data.info.name} (${crypto})</h3>
                        <div style="display: flex; justify-content: space-between; background: rgba(255,255,255,0.2); border-radius: 10px; padding: 15px; margin-top: 15px;">
                            <div style="text-align: center; flex: 1;">
                                <h4>${arb.percentage_diff.toFixed(3)}%</h4>
                                <p>套利空间</p>
                            </div>
                            <div style="text-align: center; flex: 1;">
                                <h4>$${arb.min_price.toFixed(4)}</h4>
                                <p>买入: ${arb.min_exchange}</p>
                            </div>
                            <div style="text-align: center; flex: 1;">
                                <h4>$${arb.max_price.toFixed(4)}</h4>
                                <p>卖出: ${arb.max_exchange}</p>
                            </div>
                            <div style="text-align: center; flex: 1;">
                                <h4>$${arb.price_diff.toFixed(4)}</h4>
                                <p>价差利润</p>
                            </div>
                        </div>
                        <div style="margin-top: 15px; text-align: center;">
                            <strong>最高价格: ${arb.max_exchange} $${arb.max_price.toFixed(4)} | 最低价格: ${arb.min_exchange} $${arb.min_price.toFixed(4)} | 相差: $${arb.price_diff.toFixed(4)}</strong>
                        </div>
                    </div>
                `;
            }
        }

        // 更新套利排行榜
        function updateArbitrageRanking(arbitrageData) {
            const tableBody = document.getElementById('rankingTableBody');

            if (!arbitrageData || Object.keys(arbitrageData).length === 0) {
                tableBody.innerHTML = '<tr><td colspan="7" class="text-center">等待扫描数据...</td></tr>';
                return;
            }

            const sortedOpportunities = Object.entries(arbitrageData)
                .sort((a, b) => b[1].arbitrage.percentage_diff - a[1].arbitrage.percentage_diff);

            let html = '';
            sortedOpportunities.forEach(([crypto, data], index) => {
                const arb = data.arbitrage;
                const rankIcon = index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : `${index + 1}.`;

                html += `
                    <tr>
                        <td><strong>${rankIcon}</strong></td>
                        <td><strong>${data.info.name} (${crypto})</strong></td>
                        <td><span class="badge bg-warning">${arb.percentage_diff.toFixed(3)}%</span></td>
                        <td>${arb.min_exchange}<br>$${arb.min_price.toFixed(4)}</td>
                        <td>${arb.max_exchange}<br>$${arb.max_price.toFixed(4)}</td>
                        <td>$${arb.price_diff.toFixed(4)}</td>
                        <td>${arb.exchange_count} 个交易所</td>
                    </tr>
                `;
            });

            tableBody.innerHTML = html;
        }

        // 更新价格表格
        function updatePriceTable(pricesData) {
            const priceTableDiv = document.getElementById('priceTable');

            if (!pricesData || Object.keys(pricesData).length === 0) {
                priceTableDiv.innerHTML = '<p class="text-center">等待价格数据...</p>';
                return;
            }

            const allExchanges = new Set();
            Object.values(pricesData).forEach(cryptoPrices => {
                Object.keys(cryptoPrices).forEach(exchange => allExchanges.add(exchange));
            });

            let html = `
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>币种</th>
                            ${Array.from(allExchanges).map(ex => `<th>${ex}</th>`).join('')}
                        </tr>
                    </thead>
                    <tbody>
            `;

            Object.entries(pricesData).forEach(([crypto, prices]) => {
                html += `<tr>`;
                html += `<td><strong>${crypto}</strong></td>`;

                Array.from(allExchanges).forEach(exchange => {
                    if (prices[exchange]) {
                        const price = prices[exchange].price;
                        html += `<td style="font-weight: bold; font-family: monospace;">$${price.toFixed(4)}</td>`;
                    } else {
                        html += `<td class="text-muted">--</td>`;
                    }
                });

                html += `</tr>`;
            });

            html += '</tbody></table>';
            priceTableDiv.innerHTML = html;
        }

        // 更新最后更新时间
        function updateLastUpdate(timestamp) {
            if (timestamp) {
                const date = new Date(timestamp);
                const timeString = date.toLocaleTimeString('zh-CN');
                document.getElementById('lastUpdate').textContent = timeString;
            }
        }

        // 获取唯一交易所数量
        function getUniqueExchangeCount(pricesData) {
            const exchanges = new Set();
            Object.values(pricesData || {}).forEach(cryptoPrices => {
                Object.keys(cryptoPrices).forEach(exchange => exchanges.add(exchange));
            });
            return exchanges.size;
        }

        // 获取最大套利百分比
        function getMaxArbitragePercentage(arbitrageData) {
            if (!arbitrageData || Object.keys(arbitrageData).length === 0) {
                return '0.00';
            }

            const maxPercentage = Math.max(
                ...Object.values(arbitrageData).map(data => data.arbitrage.percentage_diff)
            );

            return maxPercentage.toFixed(3);
        }

        // 绑定事件
        document.addEventListener('DOMContentLoaded', () => {
            document.getElementById('startBtn').addEventListener('click', startScanning);
            document.getElementById('stopBtn').addEventListener('click', stopScanning);

            // 页面加载时尝试加载数据
            loadCurrentData();
        });
    </script>
</body>
</html>
