#!/bin/bash

# 🚀 加密货币套利监控 - 快速启动脚本
# 版本: 2.0.0

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 打印彩色横幅
print_banner() {
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                                                              ║"
    echo "║           🎯 加密货币套利监控系统 v2.0                        ║"
    echo "║                                                              ║"
    echo "║              💰 实时价格 • 智能分析 • 套利机会                ║"
    echo "║                                                              ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# 打印状态消息
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_highlight() {
    echo -e "${PURPLE}🎯 $1${NC}"
}

# 检查应用程序包是否存在
check_app_exists() {
    if [ ! -d "CryptoArbitrageMonitor.app" ]; then
        print_error "应用程序包不存在！"
        echo ""
        print_info "请先运行以下命令创建应用程序包："
        echo -e "${YELLOW}  ./create_macos_app.sh${NC}"
        echo ""
        exit 1
    fi
    print_status "找到应用程序包: CryptoArbitrageMonitor.app"
}

# 检查系统环境
check_system() {
    print_info "检查系统环境..."
    
    # 检查 macOS 版本
    MACOS_VERSION=$(sw_vers -productVersion)
    print_info "macOS 版本: $MACOS_VERSION"
    
    # 检查 Python
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 --version)
        print_status "Python 环境: $PYTHON_VERSION"
    else
        print_warning "未找到 Python 3，应用程序将尝试自动处理"
    fi
    
    echo ""
}

# 显示启动选项菜单
show_menu() {
    echo -e "${CYAN}🚀 启动选项:${NC}"
    echo ""
    echo "  1. 🖱️  双击启动 (推荐)"
    echo "  2. 🖥️  命令行启动"
    echo "  3. 📱 安装到应用程序文件夹"
    echo "  4. 🔧 直接运行 Python 脚本"
    echo "  5. 📊 查看应用程序信息"
    echo "  6. 🆘 故障排除"
    echo "  7. ❌ 退出"
    echo ""
}

# 双击启动
launch_by_open() {
    print_highlight "使用 open 命令启动应用程序..."
    echo ""
    print_info "正在启动 CryptoArbitrageMonitor.app..."
    
    open CryptoArbitrageMonitor.app
    
    if [ $? -eq 0 ]; then
        print_status "应用程序启动成功！"
        echo ""
        print_info "应用程序将在新的终端窗口中运行"
        print_info "如果遇到安全提示，请按照以下步骤操作："
        echo "  1. 右键点击应用程序 → 选择'打开'"
        echo "  2. 或在系统偏好设置 → 安全性与隐私中允许运行"
    else
        print_error "应用程序启动失败"
    fi
}

# 命令行启动
launch_by_command() {
    print_highlight "使用命令行直接启动..."
    echo ""
    
    if [ -x "CryptoArbitrageMonitor.app/Contents/MacOS/CryptoArbitrageMonitor" ]; then
        print_info "正在启动应用程序..."
        ./CryptoArbitrageMonitor.app/Contents/MacOS/CryptoArbitrageMonitor
    else
        print_error "启动脚本不可执行，正在修复权限..."
        chmod +x CryptoArbitrageMonitor.app/Contents/MacOS/CryptoArbitrageMonitor
        ./CryptoArbitrageMonitor.app/Contents/MacOS/CryptoArbitrageMonitor
    fi
}

# 安装到应用程序文件夹
install_to_applications() {
    print_highlight "安装到应用程序文件夹..."
    echo ""
    
    if [ -d "/Applications" ]; then
        print_info "正在复制应用程序到 /Applications..."
        
        # 检查是否需要管理员权限
        if cp -R CryptoArbitrageMonitor.app /Applications/ 2>/dev/null; then
            print_status "应用程序安装成功！"
            echo ""
            print_info "现在你可以："
            echo "  • 在 Launchpad 中找到应用程序"
            echo "  • 在 Spotlight 中搜索 'Crypto Arbitrage Monitor'"
            echo "  • 在 Finder 的应用程序文件夹中找到"
        else
            print_warning "需要管理员权限，正在使用 sudo..."
            sudo cp -R CryptoArbitrageMonitor.app /Applications/
            
            if [ $? -eq 0 ]; then
                print_status "应用程序安装成功！"
            else
                print_error "安装失败"
            fi
        fi
    else
        print_error "应用程序文件夹不存在"
    fi
}

# 直接运行 Python 脚本
run_python_script() {
    print_highlight "直接运行 Python 脚本..."
    echo ""
    
    if [ -f "crypto_monitor_standalone.py" ]; then
        print_info "运行独立 Python 脚本..."
        python3 crypto_monitor_standalone.py
    elif [ -f "CryptoArbitrageMonitor.app/Contents/Resources/crypto_monitor_standalone.py" ]; then
        print_info "运行应用程序包中的 Python 脚本..."
        cd CryptoArbitrageMonitor.app/Contents/Resources/
        python3 crypto_monitor_standalone.py
        cd - > /dev/null
    else
        print_error "未找到 Python 脚本文件"
    fi
}

# 显示应用程序信息
show_app_info() {
    print_highlight "应用程序信息"
    echo ""
    
    if [ -f "CryptoArbitrageMonitor.app/Contents/Info.plist" ]; then
        echo -e "${CYAN}📱 基本信息:${NC}"
        echo "  • 应用程序名称: Crypto Arbitrage Monitor"
        echo "  • 版本: 2.0.0"
        echo "  • Bundle ID: com.crypto.arbitrage.monitor"
        echo "  • 兼容性: macOS 10.13+"
        echo ""
        
        echo -e "${CYAN}💰 功能特色:${NC}"
        echo "  • 10种主流加密货币监控"
        echo "  • 9个主要交易所价格对比"
        echo "  • 实时套利机会分析"
        echo "  • 智能排行榜显示"
        echo ""
        
        echo -e "${CYAN}📊 支持的加密货币:${NC}"
        echo "  • BTC (比特币)    • ETH (以太坊)    • BNB (币安币)"
        echo "  • XRP (瑞波币)    • ADA (卡尔达诺)  • DOGE (狗狗币)"
        echo "  • SOL (索拉纳)    • TRX (波场)      • DOT (波卡)"
        echo "  • AVAX (雪崩)"
        echo ""
        
        echo -e "${CYAN}🏢 支持的交易所:${NC}"
        echo "  • Binance  • OKX      • Bybit    • MEXC     • KuCoin"
        echo "  • Gemini   • Gate.io  • Coinbase • Kraken"
        echo ""
        
        # 显示文件大小
        APP_SIZE=$(du -sh CryptoArbitrageMonitor.app | cut -f1)
        echo -e "${CYAN}📦 应用程序大小:${NC} $APP_SIZE"
    else
        print_error "无法读取应用程序信息"
    fi
}

# 故障排除
troubleshooting() {
    print_highlight "故障排除指南"
    echo ""
    
    echo -e "${CYAN}🔧 常见问题解决方案:${NC}"
    echo ""
    
    echo -e "${YELLOW}问题1: 无法打开应用程序 (安全提示)${NC}"
    echo "解决方案:"
    echo "  1. 右键点击应用程序 → 选择'打开'"
    echo "  2. 系统偏好设置 → 安全性与隐私 → 点击'仍要打开'"
    echo "  3. 命令行: xattr -d com.apple.quarantine CryptoArbitrageMonitor.app"
    echo ""
    
    echo -e "${YELLOW}问题2: Python 依赖缺失${NC}"
    echo "解决方案:"
    echo "  1. 手动安装: pip3 install requests pytz websocket-client"
    echo "  2. 使用虚拟环境: python3 -m venv .venv && source .venv/bin/activate"
    echo ""
    
    echo -e "${YELLOW}问题3: 网络连接错误${NC}"
    echo "解决方案:"
    echo "  1. 检查网络连接"
    echo "  2. 确认防火墙设置"
    echo "  3. 尝试使用 VPN"
    echo ""
    
    echo -e "${YELLOW}问题4: 权限问题${NC}"
    echo "解决方案:"
    echo "  1. 修复权限: chmod +x CryptoArbitrageMonitor.app/Contents/MacOS/*"
    echo "  2. 重新创建应用程序包: ./create_macos_app.sh"
    echo ""
    
    print_info "更多帮助请查看:"
    echo "  • MACOS_APP_PACKAGE_GUIDE.md - 完整使用指南"
    echo "  • TROUBLESHOOTING_GUIDE.md - 详细故障排除"
    echo "  • CryptoArbitrageMonitor.app/Contents/Resources/使用说明.txt"
}

# 主菜单循环
main_menu() {
    while true; do
        show_menu
        read -p "请选择操作 (1-7): " choice
        echo ""
        
        case $choice in
            1)
                launch_by_open
                break
                ;;
            2)
                launch_by_command
                break
                ;;
            3)
                install_to_applications
                echo ""
                read -p "按回车键继续..."
                echo ""
                ;;
            4)
                run_python_script
                break
                ;;
            5)
                show_app_info
                echo ""
                read -p "按回车键继续..."
                echo ""
                ;;
            6)
                troubleshooting
                echo ""
                read -p "按回车键继续..."
                echo ""
                ;;
            7)
                print_info "再见！"
                exit 0
                ;;
            *)
                print_error "无效选择，请输入 1-7"
                echo ""
                ;;
        esac
    done
}

# 主函数
main() {
    print_banner
    echo ""
    check_app_exists
    check_system
    main_menu
}

# 执行主函数
main
