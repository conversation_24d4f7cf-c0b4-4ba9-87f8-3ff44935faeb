/* 多交易所价格监控系统样式 */

body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.card {
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s;
}

.card:hover {
    transform: translateY(-2px);
}

.card-header {
    border-radius: 10px 10px 0 0 !important;
    font-weight: bold;
}

.badge {
    font-size: 0.9rem;
    padding: 8px 12px;
}

/* 状态指示器 */
.status-running {
    background-color: #28a745 !important;
    animation: pulse 2s infinite;
}

.status-stopped {
    background-color: #6c757d !important;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* 价格表格样式 */
.price-table {
    font-size: 0.9rem;
}

.price-cell {
    font-weight: bold;
    font-family: 'Courier New', monospace;
}

.price-up {
    color: #28a745;
    background-color: rgba(40, 167, 69, 0.1);
}

.price-down {
    color: #dc3545;
    background-color: rgba(220, 53, 69, 0.1);
}

.price-neutral {
    color: #6c757d;
}

/* 套利机会样式 */
.arbitrage-high {
    background-color: rgba(255, 193, 7, 0.2);
    border-left: 4px solid #ffc107;
}

.arbitrage-medium {
    background-color: rgba(40, 167, 69, 0.2);
    border-left: 4px solid #28a745;
}

.arbitrage-low {
    background-color: rgba(108, 117, 125, 0.2);
    border-left: 4px solid #6c757d;
}

/* 最佳套利机会卡片 */
.best-arbitrage {
    background: linear-gradient(135deg, #ffc107, #ff8c00);
    color: white;
    border-radius: 15px;
    padding: 20px;
    margin: 10px 0;
}

.best-arbitrage h3 {
    margin-bottom: 15px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.arbitrage-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255,255,255,0.2);
    border-radius: 10px;
    padding: 15px;
    margin-top: 15px;
}

.arbitrage-details .detail-item {
    text-align: center;
    flex: 1;
}

.arbitrage-details .detail-item h4 {
    margin: 0;
    font-size: 1.2rem;
}

.arbitrage-details .detail-item p {
    margin: 5px 0 0 0;
    font-size: 0.9rem;
    opacity: 0.9;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .arbitrage-details {
        flex-direction: column;
        gap: 10px;
    }
    
    .arbitrage-details .detail-item {
        width: 100%;
    }
    
    .table-responsive {
        font-size: 0.8rem;
    }
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 交易所标签 */
.exchange-badge {
    font-size: 0.75rem;
    padding: 4px 8px;
    border-radius: 12px;
    margin: 2px;
    display: inline-block;
}

.exchange-binance { background-color: #f3ba2f; color: #000; }
.exchange-okx { background-color: #000; color: #fff; }
.exchange-bybit { background-color: #f7931a; color: #fff; }
.exchange-mexc { background-color: #00d4aa; color: #fff; }
.exchange-kucoin { background-color: #24ae8f; color: #fff; }
.exchange-gemini { background-color: #00dcfa; color: #000; }
.exchange-gateio { background-color: #2b73d9; color: #fff; }
.exchange-coinbase { background-color: #0052ff; color: #fff; }
.exchange-kraken { background-color: #5741d9; color: #fff; }

/* 数据更新闪烁效果 */
.data-updated {
    animation: highlight 0.5s ease-in-out;
}

@keyframes highlight {
    0% { background-color: rgba(255, 193, 7, 0.3); }
    100% { background-color: transparent; }
}

/* 错误状态 */
.error-state {
    color: #dc3545;
    background-color: rgba(220, 53, 69, 0.1);
    border: 1px solid rgba(220, 53, 69, 0.2);
    border-radius: 5px;
    padding: 10px;
}

/* 成功状态 */
.success-state {
    color: #28a745;
    background-color: rgba(40, 167, 69, 0.1);
    border: 1px solid rgba(40, 167, 69, 0.2);
    border-radius: 5px;
    padding: 10px;
}
