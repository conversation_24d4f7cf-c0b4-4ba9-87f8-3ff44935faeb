// 多交易所价格监控系统 JavaScript

class ArbitrageMonitor {
    constructor() {
        this.socket = io();
        this.isScanning = false;
        this.currentData = {};
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupSocketListeners();
        this.loadCurrentData();
    }

    setupEventListeners() {
        // 启动/停止按钮
        document.getElementById('startBtn').addEventListener('click', () => this.startScanning());
        document.getElementById('stopBtn').addEventListener('click', () => this.stopScanning());
    }

    setupSocketListeners() {
        this.socket.on('connect', () => {
            console.log('✅ Connected to server');
        });

        this.socket.on('price_update', (data) => {
            console.log('📊 Received price update:', data);
            this.updateData(data);
        });

        this.socket.on('disconnect', () => {
            console.log('❌ Disconnected from server');
            this.updateStatus('连接断开', 'danger');
        });
    }

    async startScanning() {
        try {
            console.log('🚀 Starting scan...');
            const response = await fetch('/api/start_scan');
            const result = await response.json();
            console.log('📡 Start scan response:', result);

            if (result.status === 'started' || result.status === 'already_running') {
                this.isScanning = true;
                this.updateStatus('扫描中', 'success');
                document.getElementById('startBtn').disabled = true;
                document.getElementById('stopBtn').disabled = false;

                // 立即加载一次数据
                setTimeout(() => this.loadCurrentData(), 2000);
            }
        } catch (error) {
            console.error('❌ Error starting scan:', error);
            this.updateStatus('启动失败', 'danger');
        }
    }

    async stopScanning() {
        try {
            const response = await fetch('/api/stop_scan');
            const result = await response.json();
            
            if (result.status === 'stopped') {
                this.isScanning = false;
                this.updateStatus('已停止', 'secondary');
                document.getElementById('startBtn').disabled = false;
                document.getElementById('stopBtn').disabled = true;
            }
        } catch (error) {
            console.error('Error stopping scan:', error);
        }
    }

    async loadCurrentData() {
        try {
            const response = await fetch('/api/current_data');
            const data = await response.json();
            this.updateData(data);
        } catch (error) {
            console.error('Error loading current data:', error);
        }
    }

    updateData(data) {
        console.log('🔄 Updating data:', data);
        this.currentData = data;
        this.updateStatistics(data);
        this.updateBestArbitrage(data.arbitrage);
        this.updateArbitrageRanking(data.arbitrage);
        this.updatePriceTable(data.prices);
        this.updateLastUpdate(data.timestamp);
        console.log('✅ Data update complete');
    }

    updateStatus(text, type) {
        const statusElement = document.getElementById('status');
        statusElement.textContent = text;
        statusElement.className = `badge bg-${type}`;
        
        if (type === 'success') {
            statusElement.classList.add('status-running');
        } else {
            statusElement.classList.remove('status-running');
        }
    }

    updateStatistics(data) {
        // 更新统计数据
        const cryptoCount = Object.keys(data.prices || {}).length;
        const exchangeCount = this.getUniqueExchangeCount(data.prices);
        const maxArbitrage = this.getMaxArbitragePercentage(data.arbitrage);

        document.getElementById('cryptoCount').textContent = cryptoCount;
        document.getElementById('exchangeCount').textContent = exchangeCount;
        document.getElementById('maxArbitrage').textContent = maxArbitrage + '%';
    }

    updateBestArbitrage(arbitrageData) {
        const bestArbitrageDiv = document.getElementById('bestArbitrage');
        
        if (!arbitrageData || Object.keys(arbitrageData).length === 0) {
            bestArbitrageDiv.innerHTML = `
                <h4>等待扫描数据...</h4>
                <p>点击"启动扫描"开始监控套利机会</p>
            `;
            return;
        }

        // 找出最佳套利机会
        const sortedOpportunities = Object.entries(arbitrageData)
            .sort((a, b) => b[1].arbitrage.percentage_diff - a[1].arbitrage.percentage_diff);

        if (sortedOpportunities.length > 0) {
            const [crypto, data] = sortedOpportunities[0];
            const arb = data.arbitrage;
            
            bestArbitrageDiv.innerHTML = `
                <div class="best-arbitrage">
                    <h3><i class="fas fa-trophy"></i> ${data.info.name} (${crypto})</h3>
                    <div class="arbitrage-details">
                        <div class="detail-item">
                            <h4>${arb.percentage_diff.toFixed(3)}%</h4>
                            <p>套利空间</p>
                        </div>
                        <div class="detail-item">
                            <h4>$${arb.min_price.toFixed(4)}</h4>
                            <p>买入: ${arb.min_exchange}</p>
                        </div>
                        <div class="detail-item">
                            <h4>$${arb.max_price.toFixed(4)}</h4>
                            <p>卖出: ${arb.max_exchange}</p>
                        </div>
                        <div class="detail-item">
                            <h4>$${arb.price_diff.toFixed(4)}</h4>
                            <p>价差利润</p>
                        </div>
                    </div>
                    <div class="mt-3 text-center">
                        <strong>最高价格: ${arb.max_exchange} $${arb.max_price.toFixed(4)} | 最低价格: ${arb.min_exchange} $${arb.min_price.toFixed(4)} | 相差: $${arb.price_diff.toFixed(4)}</strong>
                    </div>
                </div>
            `;
        }
    }

    updateArbitrageRanking(arbitrageData) {
        const tableBody = document.getElementById('rankingTableBody');
        
        if (!arbitrageData || Object.keys(arbitrageData).length === 0) {
            tableBody.innerHTML = '<tr><td colspan="7" class="text-center">等待扫描数据...</td></tr>';
            return;
        }

        // 排序套利机会
        const sortedOpportunities = Object.entries(arbitrageData)
            .sort((a, b) => b[1].arbitrage.percentage_diff - a[1].arbitrage.percentage_diff);

        let html = '';
        sortedOpportunities.forEach(([crypto, data], index) => {
            const arb = data.arbitrage;
            const rankIcon = index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : `${index + 1}.`;
            
            html += `
                <tr class="${this.getArbitrageRowClass(arb.percentage_diff)}">
                    <td><strong>${rankIcon}</strong></td>
                    <td><strong>${data.info.name} (${crypto})</strong></td>
                    <td><span class="badge bg-warning">${arb.percentage_diff.toFixed(3)}%</span></td>
                    <td><span class="exchange-badge">${arb.min_exchange}</span><br>$${arb.min_price.toFixed(4)}</td>
                    <td><span class="exchange-badge">${arb.max_exchange}</span><br>$${arb.max_price.toFixed(4)}</td>
                    <td class="price-cell">$${arb.price_diff.toFixed(4)}</td>
                    <td>${arb.exchange_count} 个交易所</td>
                </tr>
            `;
        });

        tableBody.innerHTML = html;
    }

    updatePriceTable(pricesData) {
        const priceTableDiv = document.getElementById('priceTable');
        
        if (!pricesData || Object.keys(pricesData).length === 0) {
            priceTableDiv.innerHTML = '<p class="text-center">等待价格数据...</p>';
            return;
        }

        // 获取所有交易所
        const allExchanges = new Set();
        Object.values(pricesData).forEach(cryptoPrices => {
            Object.keys(cryptoPrices).forEach(exchange => allExchanges.add(exchange));
        });

        let html = `
            <table class="table table-striped table-hover price-table">
                <thead class="table-dark">
                    <tr>
                        <th>币种</th>
                        ${Array.from(allExchanges).map(ex => `<th>${ex}</th>`).join('')}
                    </tr>
                </thead>
                <tbody>
        `;

        Object.entries(pricesData).forEach(([crypto, prices]) => {
            html += `<tr>`;
            html += `<td><strong>${crypto}</strong></td>`;
            
            Array.from(allExchanges).forEach(exchange => {
                if (prices[exchange]) {
                    const price = prices[exchange].price;
                    html += `<td class="price-cell">$${price.toFixed(4)}</td>`;
                } else {
                    html += `<td class="text-muted">--</td>`;
                }
            });
            
            html += `</tr>`;
        });

        html += '</tbody></table>';
        priceTableDiv.innerHTML = html;
    }

    updateLastUpdate(timestamp) {
        if (timestamp) {
            const date = new Date(timestamp);
            const timeString = date.toLocaleTimeString('zh-CN');
            document.getElementById('lastUpdate').textContent = timeString;
        }
    }

    getUniqueExchangeCount(pricesData) {
        const exchanges = new Set();
        Object.values(pricesData || {}).forEach(cryptoPrices => {
            Object.keys(cryptoPrices).forEach(exchange => exchanges.add(exchange));
        });
        return exchanges.size;
    }

    getMaxArbitragePercentage(arbitrageData) {
        if (!arbitrageData || Object.keys(arbitrageData).length === 0) {
            return '0.00';
        }
        
        const maxPercentage = Math.max(
            ...Object.values(arbitrageData).map(data => data.arbitrage.percentage_diff)
        );
        
        return maxPercentage.toFixed(3);
    }

    getArbitrageRowClass(percentage) {
        if (percentage >= 0.5) return 'arbitrage-high';
        if (percentage >= 0.2) return 'arbitrage-medium';
        return 'arbitrage-low';
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new ArbitrageMonitor();
});
