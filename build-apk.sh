#!/bin/bash

# 🚀 加密货币套利监控 APK 构建脚本
# 使用Apache Cordova将Web应用打包为Android APK

echo "🚀 开始构建加密货币套利监控 APK..."

# 检查依赖
echo "📋 检查环境依赖..."

# 检查Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js"
    echo "📥 下载地址: https://nodejs.org"
    exit 1
fi

# 检查npm
if ! command -v npm &> /dev/null; then
    echo "❌ npm 未安装"
    exit 1
fi

echo "✅ Node.js 版本: $(node --version)"
echo "✅ npm 版本: $(npm --version)"

# 检查Cordova
if ! command -v cordova &> /dev/null; then
    echo "📦 安装 Cordova..."
    npm install -g cordova
    if [ $? -ne 0 ]; then
        echo "❌ Cordova 安装失败"
        exit 1
    fi
fi

echo "✅ Cordova 版本: $(cordova --version)"

# 创建项目目录
PROJECT_NAME="crypto-arbitrage-mobile"
APP_ID="com.cryptoarbitrage.monitor"
APP_NAME="Crypto Arbitrage Monitor"

echo "📁 创建 Cordova 项目..."

# 删除已存在的项目目录
if [ -d "$PROJECT_NAME" ]; then
    echo "🗑️  删除已存在的项目目录..."
    rm -rf "$PROJECT_NAME"
fi

# 创建新的Cordova项目
cordova create "$PROJECT_NAME" "$APP_ID" "$APP_NAME"
if [ $? -ne 0 ]; then
    echo "❌ Cordova 项目创建失败"
    exit 1
fi

cd "$PROJECT_NAME"

echo "📱 添加 Android 平台..."
cordova platform add android
if [ $? -ne 0 ]; then
    echo "❌ Android 平台添加失败"
    echo "💡 请确保已安装 Android SDK 和配置 ANDROID_HOME 环境变量"
    exit 1
fi

echo "🔌 安装必要插件..."
cordova plugin add cordova-plugin-whitelist
cordova plugin add cordova-plugin-network-information
cordova plugin add cordova-plugin-device
cordova plugin add cordova-plugin-statusbar
cordova plugin add cordova-plugin-splashscreen

echo "📄 复制 Web 应用文件..."

# 复制HTML文件
cp ../templates/mobile.html www/index.html

# 创建CSS目录并复制样式文件
mkdir -p www/css
cp ../static/css/style.css www/css/ 2>/dev/null || echo "⚠️  CSS文件不存在，跳过"

# 创建JS目录
mkdir -p www/js

# 复制manifest文件
cp ../static/manifest.json www/ 2>/dev/null || echo "⚠️  manifest.json不存在，跳过"

# 创建图标目录
mkdir -p www/img

echo "⚙️  配置 config.xml..."

# 创建config.xml配置
cat > config.xml << EOF
<?xml version='1.0' encoding='utf-8'?>
<widget id="$APP_ID" version="1.0.0" xmlns="http://www.w3.org/ns/widgets" xmlns:cdv="http://cordova.apache.org/ns/1.0">
    <name>$APP_NAME</name>
    <description>实时监控加密货币套利机会的专业移动应用</description>
    <author email="<EMAIL>" href="https://cryptoarbitrage.com">
        Crypto Arbitrage Team
    </author>
    <content src="index.html" />
    <access origin="*" />
    <allow-intent href="http://*/*" />
    <allow-intent href="https://*/*" />
    <allow-intent href="tel:*" />
    <allow-intent href="sms:*" />
    <allow-intent href="mailto:*" />
    <allow-intent href="geo:*" />
    
    <platform name="android">
        <allow-intent href="market:*" />
        <preference name="android-minSdkVersion" value="22" />
        <preference name="android-targetSdkVersion" value="33" />
        <preference name="android-compileSdkVersion" value="33" />
        
        <!-- 网络权限 -->
        <config-file target="AndroidManifest.xml" parent="/manifest">
            <uses-permission android:name="android.permission.INTERNET" />
            <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
            <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
        </config-file>
        
        <!-- 应用图标 -->
        <icon density="ldpi" src="www/img/icon-36.png" />
        <icon density="mdpi" src="www/img/icon-48.png" />
        <icon density="hdpi" src="www/img/icon-72.png" />
        <icon density="xhdpi" src="www/img/icon-96.png" />
        <icon density="xxhdpi" src="www/img/icon-144.png" />
        <icon density="xxxhdpi" src="www/img/icon-192.png" />
    </platform>
    
    <!-- 全局配置 -->
    <preference name="DisallowOverscroll" value="true" />
    <preference name="android-minSdkVersion" value="22" />
    <preference name="android-targetSdkVersion" value="33" />
    <preference name="Fullscreen" value="false" />
    <preference name="Orientation" value="portrait" />
    
    <!-- 状态栏配置 -->
    <preference name="StatusBarOverlaysWebView" value="false" />
    <preference name="StatusBarBackgroundColor" value="#667eea" />
    <preference name="StatusBarStyle" value="lightcontent" />
    
    <!-- 启动画面 -->
    <preference name="SplashMaintainAspectRatio" value="true" />
    <preference name="SplashShowOnlyFirstTime" value="false" />
    <preference name="SplashScreen" value="screen" />
    <preference name="SplashScreenDelay" value="3000" />
</widget>
EOF

echo "🎨 创建简单图标..."

# 创建简单的应用图标 (使用ImageMagick，如果可用)
if command -v convert &> /dev/null; then
    echo "🖼️  生成应用图标..."
    
    # 创建基础图标
    convert -size 192x192 xc:"#667eea" -fill white -gravity center -pointsize 60 -annotate +0+0 "₿" www/img/icon-192.png
    convert www/img/icon-192.png -resize 144x144 www/img/icon-144.png
    convert www/img/icon-192.png -resize 96x96 www/img/icon-96.png
    convert www/img/icon-192.png -resize 72x72 www/img/icon-72.png
    convert www/img/icon-192.png -resize 48x48 www/img/icon-48.png
    convert www/img/icon-192.png -resize 36x36 www/img/icon-36.png
    
    echo "✅ 图标生成完成"
else
    echo "⚠️  ImageMagick 未安装，跳过图标生成"
    echo "💡 你可以手动添加图标到 www/img/ 目录"
fi

echo "🔨 构建 APK..."

# 构建调试版本
cordova build android --debug

if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 APK 构建成功！"
    echo ""
    echo "📱 APK 文件位置:"
    echo "   调试版本: $(pwd)/platforms/android/app/build/outputs/apk/debug/app-debug.apk"
    echo ""
    echo "📋 安装说明:"
    echo "   1. 将 APK 文件传输到 Android 设备"
    echo "   2. 在设备上启用 '未知来源' 安装"
    echo "   3. 点击 APK 文件进行安装"
    echo ""
    echo "🔧 构建发布版本 (可选):"
    echo "   cordova build android --release"
    echo ""
    echo "🌐 Web 版本访问地址:"
    echo "   移动端: http://localhost:8080/mobile"
    echo "   桌面端: http://localhost:8080"
    echo ""
    
    # 显示APK文件大小
    APK_FILE="platforms/android/app/build/outputs/apk/debug/app-debug.apk"
    if [ -f "$APK_FILE" ]; then
        APK_SIZE=$(du -h "$APK_FILE" | cut -f1)
        echo "📦 APK 文件大小: $APK_SIZE"
    fi
    
else
    echo "❌ APK 构建失败"
    echo ""
    echo "🔍 常见问题解决方案:"
    echo "   1. 确保已安装 Android Studio 和 Android SDK"
    echo "   2. 设置 ANDROID_HOME 环境变量"
    echo "   3. 确保 Java JDK 已正确安装"
    echo "   4. 运行 'cordova requirements' 检查环境"
    echo ""
    exit 1
fi

echo ""
echo "✨ 构建完成！享受你的加密货币套利监控应用！"
