#!/usr/bin/env python3
"""
简化版Web App - 用于测试数据显示
"""

from flask import Flask, render_template, jsonify
import requests
import threading
import time
from datetime import datetime

app = Flask(__name__)

# 全局变量
current_data = {}
scanning_active = False

def get_btc_prices():
    """获取BTC价格"""
    prices = {}
    
    # Binance
    try:
        response = requests.get("https://api.binance.com/api/v3/ticker/price?symbol=BTCUSDT", timeout=5)
        if response.status_code == 200:
            prices['Binance'] = float(response.json()['price'])
    except:
        pass
    
    # OKX
    try:
        response = requests.get("https://www.okx.com/api/v5/market/ticker?instId=BTC-USDT", timeout=5)
        if response.status_code == 200:
            data = response.json()
            if data.get('data'):
                prices['OKX'] = float(data['data'][0]['last'])
    except:
        pass
    
    # MEXC
    try:
        response = requests.get("https://api.mexc.com/api/v3/ticker/price?symbol=BTCUSDT", timeout=5)
        if response.status_code == 200:
            prices['MEXC'] = float(response.json()['price'])
    except:
        pass
    
    # Gemini
    try:
        response = requests.get("https://api.gemini.com/v1/pubticker/btcusd", timeout=5)
        if response.status_code == 200:
            prices['Gemini'] = float(response.json()['last'])
    except:
        pass
    
    return prices

def calculate_arbitrage(prices):
    """计算套利机会"""
    if len(prices) < 2:
        return None
        
    price_values = list(prices.values())
    max_price = max(price_values)
    min_price = min(price_values)
    price_diff = max_price - min_price
    percentage_diff = (price_diff / min_price) * 100
    
    max_exchange = next(ex for ex, price in prices.items() if price == max_price)
    min_exchange = next(ex for ex, price in prices.items() if price == min_price)
    
    return {
        'max_exchange': max_exchange,
        'max_price': max_price,
        'min_exchange': min_exchange,
        'min_price': min_price,
        'price_diff': price_diff,
        'percentage_diff': percentage_diff
    }

def background_scanner():
    """后台扫描"""
    global current_data, scanning_active
    
    while scanning_active:
        print("🔍 扫描BTC价格...")
        prices = get_btc_prices()
        arbitrage = calculate_arbitrage(prices) if len(prices) >= 2 else None
        
        current_data = {
            'prices': prices,
            'arbitrage': arbitrage,
            'timestamp': datetime.now().isoformat(),
            'exchange_count': len(prices)
        }
        
        print(f"✅ 获取到 {len(prices)} 个交易所价格")
        if arbitrage:
            print(f"💰 套利机会: {arbitrage['percentage_diff']:.3f}%")
        
        time.sleep(10)

@app.route('/')
def index():
    """主页"""
    return '''
    <!DOCTYPE html>
    <html>
    <head>
        <title>简化版价格监控</title>
        <meta charset="UTF-8">
        <style>
            body { font-family: Arial; margin: 20px; }
            .card { border: 1px solid #ddd; padding: 20px; margin: 10px 0; border-radius: 5px; }
            .price { font-size: 18px; font-weight: bold; }
            .arbitrage { background: #fff3cd; padding: 15px; border-radius: 5px; }
            button { padding: 10px 20px; margin: 5px; font-size: 16px; }
            .success { background: #28a745; color: white; }
            .danger { background: #dc3545; color: white; }
        </style>
    </head>
    <body>
        <h1>🚀 简化版价格监控</h1>
        
        <div>
            <button id="startBtn" onclick="startScan()" class="success">启动扫描</button>
            <button id="stopBtn" onclick="stopScan()" class="danger" disabled>停止扫描</button>
            <span id="status">未启动</span>
        </div>
        
        <div class="card">
            <h3>📊 BTC价格</h3>
            <div id="prices">等待数据...</div>
        </div>
        
        <div class="card">
            <h3>💰 套利机会</h3>
            <div id="arbitrage">等待数据...</div>
        </div>
        
        <script>
            let scanning = false;
            
            function startScan() {
                fetch('/api/start')
                .then(response => response.json())
                .then(data => {
                    scanning = true;
                    document.getElementById('startBtn').disabled = true;
                    document.getElementById('stopBtn').disabled = false;
                    document.getElementById('status').textContent = '扫描中...';
                    updateData();
                });
            }
            
            function stopScan() {
                fetch('/api/stop')
                .then(response => response.json())
                .then(data => {
                    scanning = false;
                    document.getElementById('startBtn').disabled = false;
                    document.getElementById('stopBtn').disabled = true;
                    document.getElementById('status').textContent = '已停止';
                });
            }
            
            function updateData() {
                if (!scanning) return;
                
                fetch('/api/data')
                .then(response => response.json())
                .then(data => {
                    // 更新价格
                    let pricesHtml = '';
                    for (let exchange in data.prices) {
                        pricesHtml += `<div class="price">${exchange}: $${data.prices[exchange].toFixed(2)}</div>`;
                    }
                    document.getElementById('prices').innerHTML = pricesHtml;
                    
                    // 更新套利
                    if (data.arbitrage) {
                        let arb = data.arbitrage;
                        document.getElementById('arbitrage').innerHTML = `
                            <div class="arbitrage">
                                <strong>套利空间: ${arb.percentage_diff.toFixed(3)}%</strong><br>
                                买入: ${arb.min_exchange} $${arb.min_price.toFixed(2)}<br>
                                卖出: ${arb.max_exchange} $${arb.max_price.toFixed(2)}<br>
                                价差: $${arb.price_diff.toFixed(2)}
                            </div>
                        `;
                    } else {
                        document.getElementById('arbitrage').innerHTML = '暂无套利机会';
                    }
                    
                    // 继续更新
                    setTimeout(updateData, 5000);
                });
            }
        </script>
    </body>
    </html>
    '''

@app.route('/api/start')
def start_scan():
    """启动扫描"""
    global scanning_active
    if not scanning_active:
        scanning_active = True
        thread = threading.Thread(target=background_scanner)
        thread.daemon = True
        thread.start()
        print("🚀 扫描已启动")
    return jsonify({'status': 'started'})

@app.route('/api/stop')
def stop_scan():
    """停止扫描"""
    global scanning_active
    scanning_active = False
    print("⏹️ 扫描已停止")
    return jsonify({'status': 'stopped'})

@app.route('/api/data')
def get_data():
    """获取数据"""
    return jsonify(current_data)

if __name__ == '__main__':
    print("🚀 启动简化版Web App...")
    print("📱 访问地址: http://localhost:8081")
    app.run(debug=True, host='0.0.0.0', port=8081)
