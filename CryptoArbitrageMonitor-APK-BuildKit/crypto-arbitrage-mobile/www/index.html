<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="format-detection" content="telephone=no">
    <meta name="msapplication-tap-highlight" content="no">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="theme-color" content="#667eea">
    <meta http-equiv="Content-Security-Policy" content="default-src 'self' data: gap: https://ssl.gstatic.com 'unsafe-eval' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; media-src *; img-src 'self' data: content:; connect-src 'self' https://api.binance.com https://www.okx.com https://api.bybit.com https://api.mexc.com https://api.kucoin.com https://api.gemini.com https://api.gateio.ws https://api.coinbase.com https://api.kraken.com wss://stream.binance.com wss://ws.okx.com wss://stream.bybit.com http://*************:8080 http://localhost:8080;">
    
    <title>🚀 加密货币套利监控 v2.0</title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            overflow-x: hidden;
            min-height: 100vh;
        }
        
        .container {
            padding: 15px;
            max-width: 100vw;
        }
        
        .header {
            text-align: center;
            padding: 25px 15px;
            background: rgba(255,255,255,0.15);
            border-radius: 20px;
            margin-bottom: 20px;
            backdrop-filter: blur(15px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 1.8rem;
            margin-bottom: 10px;
            font-weight: 700;
        }
        
        .loading {
            text-align: center;
            padding: 40px 20px;
        }
        
        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid rgba(255,255,255,0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .error {
            background: rgba(255,0,0,0.2);
            border: 1px solid rgba(255,0,0,0.5);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        
        .retry-btn {
            background: rgba(255,255,255,0.2);
            border: 1px solid rgba(255,255,255,0.3);
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin-top: 15px;
        }
        
        .retry-btn:hover {
            background: rgba(255,255,255,0.3);
        }
        
        iframe {
            width: 100%;
            height: calc(100vh - 200px);
            border: none;
            border-radius: 15px;
            background: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 加密货币套利监控</h1>
            <p>实时监控 • 智能分析 • 套利机会</p>
        </div>
        
        <div id="loading" class="loading">
            <div class="loading-spinner"></div>
            <p>正在连接服务器...</p>
        </div>
        
        <div id="error" class="error" style="display: none;">
            <h3>❌ 连接失败</h3>
            <p>无法连接到服务器，请确保:</p>
            <ul style="text-align: left; margin: 15px 0;">
                <li>设备已连接到网络</li>
                <li>服务器正在运行 (http://*************:8080)</li>
                <li>防火墙允许访问</li>
            </ul>
            <button class="retry-btn" onclick="retryConnection()">🔄 重试连接</button>
        </div>
        
        <iframe id="webview" style="display: none;" src=""></iframe>
    </div>

    <script type="text/javascript" src="cordova.js"></script>
    <script type="text/javascript">
        // 服务器地址配置
        const SERVER_URLS = [
            'http://*************:8080',  // 局域网地址
            'http://localhost:8080',       // 本地地址
            'http://127.0.0.1:8080'       // 回环地址
        ];
        
        let currentUrlIndex = 0;
        
        function showLoading() {
            document.getElementById('loading').style.display = 'block';
            document.getElementById('error').style.display = 'none';
            document.getElementById('webview').style.display = 'none';
        }
        
        function showError() {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('error').style.display = 'block';
            document.getElementById('webview').style.display = 'none';
        }
        
        function showWebview(url) {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('error').style.display = 'none';
            document.getElementById('webview').style.display = 'block';
            document.getElementById('webview').src = url;
        }
        
        function testConnection(url) {
            return new Promise((resolve, reject) => {
                const img = new Image();
                img.onload = () => resolve(true);
                img.onerror = () => reject(false);
                img.src = url + '/static/favicon.ico?' + Date.now();
                
                // 超时处理
                setTimeout(() => reject(false), 5000);
            });
        }
        
        async function tryConnect() {
            showLoading();
            
            for (let i = 0; i < SERVER_URLS.length; i++) {
                const url = SERVER_URLS[i];
                console.log('尝试连接:', url);
                
                try {
                    await testConnection(url);
                    console.log('连接成功:', url);
                    showWebview(url);
                    return;
                } catch (e) {
                    console.log('连接失败:', url);
                }
            }
            
            // 所有地址都失败
            showError();
        }
        
        function retryConnection() {
            tryConnect();
        }
        
        // Cordova 设备就绪事件
        document.addEventListener('deviceready', function() {
            console.log('Cordova 设备就绪');
            tryConnect();
        }, false);
        
        // 如果不是 Cordova 环境，直接尝试连接
        if (!window.cordova) {
            console.log('非 Cordova 环境，直接连接');
            tryConnect();
        }
    </script>
</body>
</html>
