<?xml version='1.0' encoding='utf-8'?>
<widget id="com.cryptoarbitrage.monitor" version="1.0.0" xmlns="http://www.w3.org/ns/widgets" xmlns:cdv="http://cordova.apache.org/ns/1.0">
    <name>Crypto Arbitrage Monitor</name>
    <description>实时监控加密货币套利机会的专业移动应用</description>
    <author email="<EMAIL>" href="https://cryptoarbitrage.com">
        Crypto Arbitrage Team
    </author>
    <content src="index.html" />
    <access origin="*" />
    <allow-intent href="http://*/*" />
    <allow-intent href="https://*/*" />
    <allow-intent href="tel:*" />
    <allow-intent href="sms:*" />
    <allow-intent href="mailto:*" />
    <allow-intent href="geo:*" />
    
    <platform name="android">
        <allow-intent href="market:*" />
        <preference name="android-minSdkVersion" value="22" />
        <preference name="android-targetSdkVersion" value="33" />
        <preference name="android-compileSdkVersion" value="33" />
        
        <!-- 网络权限 -->
        <config-file target="AndroidManifest.xml" parent="/manifest">
            <uses-permission android:name="android.permission.INTERNET" />
            <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
            <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
        </config-file>
        
        <!-- 应用图标 -->
        <icon density="ldpi" src="www/img/icon-36.png" />
        <icon density="mdpi" src="www/img/icon-48.png" />
        <icon density="hdpi" src="www/img/icon-72.png" />
        <icon density="xhdpi" src="www/img/icon-96.png" />
        <icon density="xxhdpi" src="www/img/icon-144.png" />
        <icon density="xxxhdpi" src="www/img/icon-192.png" />
        
        <!-- 启动画面 -->
        <splash density="land-ldpi" src="www/img/splash-320x200.png" />
        <splash density="land-mdpi" src="www/img/splash-480x320.png" />
        <splash density="land-hdpi" src="www/img/splash-800x480.png" />
        <splash density="port-ldpi" src="www/img/splash-200x320.png" />
        <splash density="port-mdpi" src="www/img/splash-320x480.png" />
        <splash density="port-hdpi" src="www/img/splash-480x800.png" />
    </platform>
    
    <!-- 全局配置 -->
    <preference name="DisallowOverscroll" value="true" />
    <preference name="Fullscreen" value="false" />
    <preference name="Orientation" value="portrait" />
    <preference name="BackgroundColor" value="#667eea" />
    
    <!-- 状态栏配置 -->
    <preference name="StatusBarOverlaysWebView" value="false" />
    <preference name="StatusBarBackgroundColor" value="#667eea" />
    <preference name="StatusBarStyle" value="lightcontent" />
    
    <!-- 启动画面配置 -->
    <preference name="SplashMaintainAspectRatio" value="true" />
    <preference name="SplashShowOnlyFirstTime" value="false" />
    <preference name="SplashScreen" value="screen" />
    <preference name="SplashScreenDelay" value="3000" />
    <preference name="AutoHideSplashScreen" value="true" />
    
    <!-- 网络安全配置 -->
    <preference name="scheme" value="https" />
    <preference name="hostname" value="localhost" />
    
    <!-- 插件配置 -->
    <plugin name="cordova-plugin-whitelist" spec="1" />
    <plugin name="cordova-plugin-statusbar" spec="2" />
    <plugin name="cordova-plugin-device" spec="2" />
    <plugin name="cordova-plugin-splashscreen" spec="5" />
    <plugin name="cordova-plugin-network-information" spec="2" />
</widget>
