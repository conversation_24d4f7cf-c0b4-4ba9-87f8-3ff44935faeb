{"name": "npm-bundled", "version": "3.0.1", "description": "list things in node_modules that are bundledDependencies, or transitive dependencies thereof", "main": "lib/index.js", "repository": {"type": "git", "url": "git+https://github.com/npm/npm-bundled.git"}, "author": "GitHub Inc.", "license": "ISC", "devDependencies": {"@npmcli/eslint-config": "^4.0.0", "@npmcli/template-oss": "4.22.0", "mutate-fs": "^2.1.1", "tap": "^16.3.0"}, "scripts": {"test": "tap", "lint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\"", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "lintfix": "npm run lint -- --fix", "snap": "tap", "posttest": "npm run lint"}, "files": ["bin/", "lib/"], "dependencies": {"npm-normalize-package-bin": "^3.0.0"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.22.0", "publish": true}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"]}}