{"name": "are-we-there-yet", "version": "4.0.2", "description": "Keep track of the overall completion of many disparate processes", "main": "lib/index.js", "scripts": {"test": "tap", "lint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\"", "lintfix": "npm run lint -- --fix", "posttest": "npm run lint", "postsnap": "npm run lintfix --", "snap": "tap", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force"}, "repository": {"type": "git", "url": "https://github.com/npm/are-we-there-yet.git"}, "author": "GitHub Inc.", "license": "ISC", "bugs": {"url": "https://github.com/npm/are-we-there-yet/issues"}, "homepage": "https://github.com/npm/are-we-there-yet", "devDependencies": {"@npmcli/eslint-config": "^4.0.0", "@npmcli/template-oss": "4.21.3", "tap": "^16.0.1"}, "files": ["bin/", "lib/"], "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "tap": {"branches": 68, "statements": 92, "functions": 86, "lines": 92, "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.21.3", "publish": true}}