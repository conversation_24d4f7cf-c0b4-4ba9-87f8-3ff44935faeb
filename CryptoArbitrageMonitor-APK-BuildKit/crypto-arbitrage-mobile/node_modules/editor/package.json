{"name": "editor", "version": "1.0.0", "description": "launch $EDITOR in your program", "main": "index.js", "directories": {"example": "example", "test": "test"}, "dependencies": {}, "devDependencies": {"tap": "~0.4.4"}, "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "git://github.com/substack/node-editor.git"}, "homepage": "https://github.com/substack/node-editor", "keywords": ["text", "edit", "shell"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "engine": {"node": ">=0.6"}}