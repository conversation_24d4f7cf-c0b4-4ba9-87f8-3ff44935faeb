#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}========================================"
echo "  Crypto Arbitrage Monitor APK Builder"
echo -e "========================================${NC}"
echo ""

# 检查 Node.js
echo -e "${BLUE}Checking Node.js...${NC}"
if command -v node &> /dev/null; then
    NODE_VERSION=$(node --version)
    echo -e "${GREEN}✅ Node.js: $NODE_VERSION${NC}"
else
    echo -e "${RED}❌ Node.js not found!${NC}"
    echo "Please install Node.js from https://nodejs.org/"
    exit 1
fi

# 检查 Cordova
echo -e "${BLUE}Checking Cordova...${NC}"
if command -v cordova &> /dev/null; then
    CORDOVA_VERSION=$(cordova --version)
    echo -e "${GREEN}✅ Cordova: $CORDOVA_VERSION${NC}"
else
    echo -e "${YELLOW}⚠️  Installing Cordova...${NC}"
    npm install -g cordova
fi

# 检查 Java
echo -e "${BLUE}Checking Java...${NC}"
if command -v java &> /dev/null; then
    JAVA_VERSION=$(java -version 2>&1 | head -n 1)
    echo -e "${GREEN}✅ Java: $JAVA_VERSION${NC}"
else
    echo -e "${RED}❌ Java not found!${NC}"
    echo "Please install Java JDK 8+ from https://adoptopenjdk.net/"
    exit 1
fi

echo ""
echo -e "${BLUE}Building APK...${NC}"
cd crypto-arbitrage-mobile

# 构建 APK
cordova build android --debug

if [ $? -eq 0 ]; then
    echo ""
    echo -e "${GREEN}🎉 SUCCESS! APK built successfully!${NC}"
    echo -e "${BLUE}Location: platforms/android/app/build/outputs/apk/debug/${NC}"
    
    # 查找并显示 APK 文件
    APK_FILE=$(find platforms/android/app/build/outputs/apk/debug -name "*.apk" | head -n 1)
    if [ -n "$APK_FILE" ]; then
        echo -e "${GREEN}APK File: $APK_FILE${NC}"
        ls -lh "$APK_FILE"
    fi
else
    echo ""
    echo -e "${RED}❌ BUILD FAILED!${NC}"
    echo "Please check the error messages above."
    exit 1
fi
