# 🚀 Crypto Arbitrage Monitor APK 构建套件

## 📱 快速开始

### Windows 用户
1. 双击运行 `build_apk.bat`
2. 按照提示安装必要的依赖
3. 等待构建完成

### Linux/macOS 用户
```bash
chmod +x build_apk.sh
./build_apk.sh
```

## 🛠️ 系统要求

### 必需软件
- **Node.js** (v14+) - https://nodejs.org/
- **Java JDK** (v8+) - https://adoptopenjdk.net/
- **Android Studio** - https://developer.android.com/studio

### 环境变量 (可选)
```bash
export ANDROID_HOME=/path/to/android-sdk
export PATH=$PATH:$ANDROID_HOME/tools
export PATH=$PATH:$ANDROID_HOME/platform-tools
```

## 📦 构建输出

成功构建后，APK 文件位于:
```
crypto-arbitrage-mobile/platforms/android/app/build/outputs/apk/debug/app-debug.apk
```

## 📱 安装说明

1. 将 APK 文件传输到 Android 设备
2. 在设备设置中启用 "未知来源" 安装
3. 点击 APK 文件进行安装
4. 允许网络权限

## 🌐 使用说明

1. 在电脑上启动服务器: `python3 app.py`
2. 确保手机和电脑在同一网络
3. 打开手机应用，自动连接服务器
4. 查看实时套利机会

## 🔧 故障排除

### 常见问题
- **Node.js not found**: 安装 Node.js 并重启终端
- **Java not found**: 安装 Java JDK 8+
- **Android SDK not found**: 安装 Android Studio 或设置 ANDROID_HOME

### 构建失败
```bash
cd crypto-arbitrage-mobile
cordova clean android
cordova platform remove android
cordova platform add android
cordova build android --debug
```

## 📊 应用功能

- ✅ 10种加密货币实时监控
- ✅ 9个主要交易所价格对比
- ✅ 智能套利机会分析
- ✅ 移动端优化界面
- ✅ 自动服务器连接

---
**版本**: 2.0.0
**兼容性**: Android 5.1+
