#!/bin/bash

echo "🚀 Crypto Arbitrage Monitor - 快速安装脚本"
echo ""

# 检查操作系统
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    echo "检测到 macOS 系统"
    
    # 检查 Homebrew
    if ! command -v brew &> /dev/null; then
        echo "安装 Homebrew..."
        /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
    fi
    
    # 安装 Node.js
    if ! command -v node &> /dev/null; then
        echo "安装 Node.js..."
        brew install node
    fi
    
    # 安装 Java
    if ! command -v java &> /dev/null; then
        echo "安装 Java..."
        brew install openjdk@8
    fi
    
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    # Linux
    echo "检测到 Linux 系统"
    
    # 更新包管理器
    sudo apt-get update
    
    # 安装 Node.js
    if ! command -v node &> /dev/null; then
        echo "安装 Node.js..."
        curl -fsSL https://deb.nodesource.com/setup_16.x | sudo -E bash -
        sudo apt-get install -y nodejs
    fi
    
    # 安装 Java
    if ! command -v java &> /dev/null; then
        echo "安装 Java..."
        sudo apt-get install -y openjdk-8-jdk
    fi
fi

# 安装 Cordova
echo "安装 Cordova..."
npm install -g cordova

echo ""
echo "✅ 安装完成！现在可以运行 build_apk.sh 构建 APK"
