@echo off
echo ========================================
echo   Crypto Arbitrage Monitor APK Builder
echo ========================================
echo.

echo Checking Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js not found!
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

echo Checking Cordova...
cordova --version >nul 2>&1
if errorlevel 1 (
    echo Installing Cordova...
    npm install -g cordova
)

echo Checking Java...
java -version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Java not found!
    echo Please install Java JDK 8+ from https://adoptopenjdk.net/
    pause
    exit /b 1
)

echo.
echo Building APK...
cd crypto-arbitrage-mobile
cordova build android --debug

if errorlevel 1 (
    echo.
    echo BUILD FAILED!
    echo Please check the error messages above.
    pause
    exit /b 1
)

echo.
echo SUCCESS! APK built successfully!
echo Location: platforms\android\app\build\outputs\apk\debug\
pause
