# 🚀 多交易所价格监控 Web App

一个实时监控加密货币价格并发现套利机会的现代化Web应用程序。

## ✨ 功能特点

### 🎯 核心功能
- **实时价格监控**: 同时监控10种主流加密货币
- **多交易所支持**: 覆盖9个主要交易所
- **套利机会发现**: 自动计算并排序套利机会
- **实时数据更新**: 使用WebSocket实现实时数据推送
- **响应式设计**: 支持桌面和移动设备

### 📊 支持的加密货币
- BTC (比特币) | ETH (以太坊) | BNB (币安币) | XRP (瑞波币) | ADA (卡尔达诺)
- DOGE (狗狗币) | SOL (索拉纳) | TRX (波场) | DOT (波卡) | AVAX (雪崩)

### 🏢 支持的交易所
- Binance | OKX | Bybit | MEXC | KuCoin | Gemini | Gate.io | Coinbase | Kraken

## 🚀 快速开始

### 环境要求
- Python 3.9+
- 网络连接

### 安装步骤

1. **安装依赖**
```bash
pip install -r requirements.txt
```

2. **启动应用**
```bash
python3 app.py
```

3. **访问应用**
打开浏览器访问: http://localhost:8080

## 📱 使用说明

### 启动监控
1. 打开Web应用
2. 点击"启动扫描"按钮
3. 系统开始实时监控价格和套利机会

### 界面说明
- **状态面板**: 监控币种数、交易所数、最大套利空间、最后更新时间
- **最佳套利机会**: 突出显示当前最佳的套利机会，包含详细的买卖信息
- **套利排行榜**: 按套利空间排序的所有机会，显示前5名
- **实时价格表**: 所有币种在各交易所的实时价格矩阵

### 套利信息解读
- **套利空间**: 价差占最低价的百分比
- **买入交易所**: 价格最低的交易所
- **卖出交易所**: 价格最高的交易所
- **价差**: 最高价与最低价的差额
- **数据源**: 成功获取价格的交易所数量

## 🔧 技术架构

### 后端技术
- **Flask**: Web框架
- **Flask-SocketIO**: WebSocket支持
- **Requests**: HTTP请求库
- **Threading**: 多线程处理

### 前端技术
- **Bootstrap 5**: UI框架
- **Socket.IO**: 实时通信
- **Font Awesome**: 图标库
- **原生JavaScript**: 交互逻辑

## ⚠️ 注意事项

### 投资风险
- 套利存在风险，价格可能快速变化
- 需要考虑交易手续费和转账费用
- 建议套利空间>0.3%才考虑执行
- 本工具仅供参考，不构成投资建议

### 技术限制
- 依赖各交易所API的稳定性
- 网络延迟可能影响数据准确性
- 部分交易所可能有访问限制

## 🛠️ 开发说明

### 项目结构
```
├── app.py              # 主应用文件
├── templates/
│   └── index.html      # 前端模板
├── static/
│   ├── css/
│   │   └── style.css   # 样式文件
│   └── js/
│       └── app.js      # 前端逻辑
├── requirements.txt    # 依赖包列表
└── WEB_APP_README.md  # 说明文档
```

### API接口
- `GET /`: 主页面
- `GET /api/start_scan`: 启动扫描
- `GET /api/stop_scan`: 停止扫描
- `GET /api/current_data`: 获取当前数据
- `GET /api/scan_single/<crypto>`: 扫描单个币种

### WebSocket事件
- `connect`: 连接建立
- `price_update`: 价格数据更新
- `disconnect`: 连接断开

## 📈 扩展功能

### 可能的改进
- 添加更多交易所支持
- 实现价格预警功能
- 添加历史数据分析
- 支持更多加密货币
- 添加用户账户系统
- 实现自动交易功能

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

---

**免责声明**: 本工具仅供教育和研究目的，不构成投资建议。加密货币投资存在风险，请谨慎决策。
