#!/usr/bin/env python3
"""
多交易所价格监控 Web App
基于Flask的实时加密货币套利监控系统
"""

from flask import Flask, render_template, jsonify, request
from flask_socketio import Socket<PERSON>, emit
import requests
import threading
import time
from datetime import datetime
import pytz
import json

app = Flask(__name__)
app.config['SECRET_KEY'] = 'crypto_arbitrage_monitor_2025'
socketio = SocketIO(app, cors_allowed_origins="*")

# 全局变量
current_prices = {}
arbitrage_opportunities = {}
scanning_active = False
local_tz = pytz.timezone('Asia/Shanghai')

# 支持的加密货币
CRYPTOCURRENCIES = {
    "BTC": {"name": "比特币", "base": "USDT"},
    "ETH": {"name": "以太坊", "base": "USDT"},
    "BNB": {"name": "币安币", "base": "USDT"},
    "XRP": {"name": "瑞波币", "base": "USDT"},
    "ADA": {"name": "卡尔达诺", "base": "USDT"},
    "DOGE": {"name": "狗狗币", "base": "USDT"},
    "SOL": {"name": "索拉纳", "base": "USDT"},
    "TRX": {"name": "波场", "base": "USDT"},
    "DOT": {"name": "波卡", "base": "USDT"},
    "AVAX": {"name": "雪崩", "base": "USDT"}
}

def get_exchange_price(exchange, symbol, crypto_symbol):
    """获取单个交易所价格"""
    try:
        headers = {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"}

        if exchange == 'Binance':
            url = f"https://api.binance.com/api/v3/ticker/price?symbol={symbol}"
            response = requests.get(url, headers=headers, timeout=5)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ {exchange}: ${float(data['price']):.4f}")
                return float(data['price'])

        elif exchange == 'OKX':
            url = f"https://www.okx.com/api/v5/market/ticker?instId={symbol}"
            response = requests.get(url, headers=headers, timeout=5)
            if response.status_code == 200:
                data = response.json()
                if data.get('data'):
                    price = float(data['data'][0]['last'])
                    print(f"✅ {exchange}: ${price:.4f}")
                    return price

        elif exchange == 'Bybit':
            url = f"https://api.bybit.com/v5/market/tickers?category=spot&symbol={symbol}"
            response = requests.get(url, headers=headers, timeout=5)
            if response.status_code == 200:
                data = response.json()
                if data.get('result', {}).get('list'):
                    price = float(data['result']['list'][0]['lastPrice'])
                    print(f"✅ {exchange}: ${price:.4f}")
                    return price

        elif exchange == 'MEXC':
            url = f"https://api.mexc.com/api/v3/ticker/price?symbol={symbol}"
            response = requests.get(url, headers=headers, timeout=5)
            if response.status_code == 200:
                data = response.json()
                price = float(data['price'])
                print(f"✅ {exchange}: ${price:.4f}")
                return price

        elif exchange == 'KuCoin':
            url = f"https://api.kucoin.com/api/v1/market/orderbook/level1?symbol={symbol}"
            response = requests.get(url, headers=headers, timeout=5)
            if response.status_code == 200:
                data = response.json()
                if data.get('code') == '200000':
                    price = float(data['data']['price'])
                    print(f"✅ {exchange}: ${price:.4f}")
                    return price

        elif exchange == 'Gemini':
            url = f"https://api.gemini.com/v1/pubticker/{symbol.lower()}"
            response = requests.get(url, headers=headers, timeout=5)
            if response.status_code == 200:
                data = response.json()
                price = float(data['last'])
                print(f"✅ {exchange}: ${price:.4f}")
                return price

        elif exchange == 'Gate.io':
            url = f"https://api.gateio.ws/api/v4/spot/tickers?currency_pair={symbol}"
            response = requests.get(url, headers=headers, timeout=5)
            if response.status_code == 200:
                data = response.json()
                if data:
                    price = float(data[0]['last'])
                    print(f"✅ {exchange}: ${price:.4f}")
                    return price

        elif exchange == 'Coinbase':
            url = f"https://api.coinbase.com/v2/exchange-rates?currency={crypto_symbol}"
            response = requests.get(url, headers=headers, timeout=5)
            if response.status_code == 200:
                data = response.json()
                if 'data' in data and 'rates' in data['data']:
                    usd_rate = data['data']['rates'].get('USD')
                    if usd_rate:
                        price = float(usd_rate)
                        print(f"✅ {exchange}: ${price:.4f}")
                        return price

        elif exchange == 'Kraken':
            kraken_symbol = symbol.replace('/', '').replace('-', '')
            if kraken_symbol == 'BTCUSD':
                kraken_symbol = 'XXBTZUSD'
            elif kraken_symbol == 'ETHUSD':
                kraken_symbol = 'XETHZUSD'

            url = f"https://api.kraken.com/0/public/Ticker?pair={kraken_symbol}"
            response = requests.get(url, headers=headers, timeout=5)
            if response.status_code == 200:
                data = response.json()
                if 'result' in data and data['result']:
                    pair_data = list(data['result'].values())[0]
                    if 'c' in pair_data:
                        price = float(pair_data['c'][0])
                        print(f"✅ {exchange}: ${price:.4f}")
                        return price

    except Exception as e:
        print(f"❌ {exchange} 获取价格失败: {str(e)}")
        return None

def scan_crypto_prices(crypto_symbol):
    """扫描单个加密货币的所有交易所价格"""
    # 基础交易所配置
    exchanges_config = {
        'Binance': f'{crypto_symbol}USDT',
        'OKX': f'{crypto_symbol}-USDT',
        'Bybit': f'{crypto_symbol}USDT',
        'MEXC': f'{crypto_symbol}USDT',
        'KuCoin': f'{crypto_symbol}-USDT',
        'Gate.io': f'{crypto_symbol}_USDT',
        'Coinbase': f'{crypto_symbol}-USD',
        'Kraken': f'{crypto_symbol}USD'
    }

    # Gemini支持的币种列表
    gemini_supported = ['BTC', 'ETH', 'XRP', 'DOGE', 'SOL', 'DOT', 'AVAX']
    if crypto_symbol in gemini_supported:
        exchanges_config['Gemini'] = f'{crypto_symbol}USD'
    
    prices = {}
    for exchange, symbol_format in exchanges_config.items():
        price = get_exchange_price(exchange, symbol_format, crypto_symbol)
        if price:
            prices[exchange] = {
                'price': price,
                'timestamp': datetime.now(local_tz).isoformat(),
                'symbol': symbol_format
            }
    
    return prices

def calculate_arbitrage(prices):
    """计算套利机会"""
    if len(prices) < 2:
        return None
        
    price_values = [data['price'] for data in prices.values()]
    max_price = max(price_values)
    min_price = min(price_values)
    price_diff = max_price - min_price
    percentage_diff = (price_diff / min_price) * 100
    
    max_exchange = next(ex for ex, data in prices.items() if data['price'] == max_price)
    min_exchange = next(ex for ex, data in prices.items() if data['price'] == min_price)
    
    return {
        'max_exchange': max_exchange,
        'max_price': max_price,
        'min_exchange': min_exchange,
        'min_price': min_price,
        'price_diff': price_diff,
        'percentage_diff': percentage_diff,
        'exchange_count': len(prices)
    }

def background_scanner():
    """后台扫描线程"""
    global current_prices, arbitrage_opportunities, scanning_active

    print("🚀 后台扫描线程启动")

    while scanning_active:
        print(f"\n📊 开始新一轮扫描 - {datetime.now(local_tz).strftime('%H:%M:%S')}")
        round_opportunities = {}

        for crypto_symbol, crypto_info in CRYPTOCURRENCIES.items():
            if not scanning_active:
                break

            print(f"\n🔍 正在扫描 {crypto_info['name']} ({crypto_symbol})...")
            prices = scan_crypto_prices(crypto_symbol)
            current_prices[crypto_symbol] = prices

            print(f"   📈 获取到 {len(prices)} 个交易所价格")

            if len(prices) >= 2:  # 降低要求，至少2个交易所
                arbitrage = calculate_arbitrage(prices)
                if arbitrage:
                    round_opportunities[crypto_symbol] = {
                        'info': crypto_info,
                        'arbitrage': arbitrage,
                        'prices': prices
                    }
                    print(f"   💰 发现套利机会: {arbitrage['percentage_diff']:.3f}%")
            else:
                print(f"   ❌ 数据不足，仅获取到 {len(prices)} 个交易所")

            time.sleep(1)

        # 更新套利机会
        arbitrage_opportunities = round_opportunities

        print(f"\n📤 发送数据到前端: {len(current_prices)} 个币种, {len(arbitrage_opportunities)} 个套利机会")

        # 发送实时数据到前端
        socketio.emit('price_update', {
            'prices': current_prices,
            'arbitrage': arbitrage_opportunities,
            'timestamp': datetime.now(local_tz).isoformat()
        })

        print("⏱️  等待10秒进行下一轮扫描...")
        time.sleep(10)  # 每10秒扫描一轮

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/test')
def test_page():
    """测试页面"""
    return render_template('test.html')

@app.route('/simple')
def simple_page():
    """简化页面"""
    return render_template('simple.html')

@app.route('/api/start_scan')
def start_scan():
    """启动扫描"""
    global scanning_active
    if not scanning_active:
        scanning_active = True
        thread = threading.Thread(target=background_scanner)
        thread.daemon = True
        thread.start()
        return jsonify({'status': 'started'})
    return jsonify({'status': 'already_running'})

@app.route('/api/stop_scan')
def stop_scan():
    """停止扫描"""
    global scanning_active
    scanning_active = False
    return jsonify({'status': 'stopped'})

@app.route('/api/current_data')
def get_current_data():
    """获取当前数据"""
    return jsonify({
        'prices': current_prices,
        'arbitrage': arbitrage_opportunities,
        'timestamp': datetime.now(local_tz).isoformat()
    })

@app.route('/api/scan_single/<crypto>')
def scan_single_crypto(crypto):
    """扫描单个加密货币"""
    if crypto.upper() in CRYPTOCURRENCIES:
        prices = scan_crypto_prices(crypto.upper())
        arbitrage = calculate_arbitrage(prices) if len(prices) >= 2 else None
        
        return jsonify({
            'crypto': crypto.upper(),
            'prices': prices,
            'arbitrage': arbitrage,
            'timestamp': datetime.now(local_tz).isoformat()
        })
    return jsonify({'error': 'Cryptocurrency not supported'}), 400

@socketio.on('connect')
def handle_connect():
    """WebSocket连接处理"""
    emit('connected', {'data': 'Connected to arbitrage monitor'})

@socketio.on('disconnect')
def handle_disconnect():
    """WebSocket断开处理"""
    print('Client disconnected')

if __name__ == '__main__':
    print("🚀 启动多交易所价格监控 Web App...")
    print("📱 访问地址: http://localhost:8080")
    socketio.run(app, debug=True, host='0.0.0.0', port=8080)
