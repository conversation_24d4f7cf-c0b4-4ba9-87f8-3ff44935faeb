<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="format-detection" content="telephone=no">
    <meta name="msapplication-tap-highlight" content="no">
    <meta http-equiv="Content-Security-Policy" content="default-src 'self' data: gap: https://ssl.gstatic.com 'unsafe-eval' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; media-src *; img-src 'self' data: content:; connect-src 'self' https://api.binance.com https://www.okx.com https://api.bybit.com https://api.mexc.com https://api.kucoin.com https://api.gemini.com https://api.gateio.ws https://api.coinbase.com https://api.kraken.com;">
    
    <title>🚀 加密货币套利监控</title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            overflow-x: hidden;
            min-height: 100vh;
        }
        
        .container {
            padding: 15px;
            max-width: 100vw;
        }
        
        .header {
            text-align: center;
            padding: 25px 15px;
            background: rgba(255,255,255,0.15);
            border-radius: 20px;
            margin-bottom: 20px;
            backdrop-filter: blur(15px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 1.8rem;
            margin-bottom: 10px;
            font-weight: 700;
        }
        
        .controls {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 15px 25px;
            border: none;
            border-radius: 30px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 140px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(40,167,69,0.4);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #dc3545, #e74c3c);
            color: white;
        }
        
        .btn-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(220,53,69,0.4);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }
        
        .status {
            text-align: center;
            padding: 15px;
            border-radius: 15px;
            margin-bottom: 20px;
            background: rgba(255,255,255,0.15);
            backdrop-filter: blur(15px);
            font-weight: 600;
            font-size: 1.1rem;
        }
        
        .card {
            background: rgba(255,255,255,0.15);
            border-radius: 20px;
            padding: 20px;
            margin-bottom: 20px;
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255,255,255,0.2);
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .card h3 {
            margin-bottom: 20px;
            font-size: 1.4rem;
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 700;
        }
        
        .best-arbitrage {
            background: linear-gradient(135deg, #ffc107, #ff8c00);
            border-radius: 20px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 10px 40px rgba(255,193,7,0.3);
        }
        
        .arbitrage-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin: 20px 0;
        }
        
        .arbitrage-item {
            background: rgba(255,255,255,0.25);
            padding: 15px;
            border-radius: 15px;
            text-align: center;
            backdrop-filter: blur(10px);
        }
        
        .arbitrage-item h4 {
            font-size: 1.3rem;
            margin-bottom: 8px;
            font-weight: 700;
        }
        
        .arbitrage-item small {
            font-size: 0.9rem;
            opacity: 0.9;
            font-weight: 500;
        }
        
        .arbitrage-list {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .arbitrage-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            margin-bottom: 12px;
            background: rgba(255,255,255,0.15);
            border-radius: 15px;
            border-left: 5px solid #ffc107;
            backdrop-filter: blur(10px);
        }
        
        .arbitrage-info {
            flex: 1;
        }
        
        .arbitrage-profit {
            text-align: right;
            font-weight: 700;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .stat-card {
            background: rgba(255,255,255,0.15);
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            backdrop-filter: blur(15px);
        }
        
        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 8px;
            color: #ffc107;
        }
        
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
            font-weight: 500;
        }
        
        .loading {
            text-align: center;
            padding: 30px;
            opacity: 0.8;
        }
        
        .spinner {
            display: inline-block;
            width: 30px;
            height: 30px;
            border: 4px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
            margin-bottom: 15px;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .price-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
            gap: 12px;
        }
        
        .price-item {
            background: rgba(255,255,255,0.15);
            padding: 12px;
            border-radius: 12px;
            text-align: center;
            font-family: 'SF Mono', Monaco, monospace;
            font-size: 0.95rem;
            backdrop-filter: blur(10px);
        }
        
        /* 响应式设计 */
        @media (max-width: 480px) {
            .arbitrage-grid {
                grid-template-columns: 1fr;
            }
            
            .controls {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 200px;
            }
            
            .header h1 {
                font-size: 1.5rem;
            }
        }
        
        /* 滚动条样式 */
        ::-webkit-scrollbar {
            width: 8px;
        }
        
        ::-webkit-scrollbar-track {
            background: rgba(255,255,255,0.1);
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb {
            background: rgba(255,255,255,0.3);
            border-radius: 4px;
        }
        
        /* 状态指示器 */
        .status-running {
            background: linear-gradient(135deg, #28a745, #20c997) !important;
            animation: pulse 2s infinite;
        }
        
        .status-error {
            background: linear-gradient(135deg, #dc3545, #e74c3c) !important;
        }
        
        .status-warning {
            background: linear-gradient(135deg, #ffc107, #ff8c00) !important;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.8; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <h1>🚀 加密货币套利监控</h1>
            <div id="status" class="status">未启动</div>
        </div>

        <!-- 控制按钮 -->
        <div class="controls">
            <button id="startBtn" class="btn btn-primary" onclick="startScan()">
                ▶️ 启动扫描
            </button>
            <button id="stopBtn" class="btn btn-danger" onclick="stopScan()" disabled>
                ⏹️ 停止扫描
            </button>
        </div>

        <!-- 统计信息 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div id="cryptoCount" class="stat-value">0</div>
                <div class="stat-label">监控币种</div>
            </div>
            <div class="stat-card">
                <div id="maxArbitrage" class="stat-value">0.00%</div>
                <div class="stat-label">最大套利</div>
            </div>
        </div>

        <!-- 最佳套利机会 -->
        <div class="card">
            <h3>🏆 最佳套利机会</h3>
            <div id="bestArbitrage" class="loading">
                <div class="spinner"></div>
                <div>等待扫描数据...</div>
            </div>
        </div>

        <!-- 套利排行榜 -->
        <div class="card">
            <h3>📊 套利排行榜</h3>
            <div id="arbitrageList" class="arbitrage-list loading">
                <div class="spinner"></div>
                <div>等待扫描数据...</div>
            </div>
        </div>

        <!-- 实时价格 -->
        <div class="card">
            <h3>💱 实时价格</h3>
            <div id="priceGrid" class="loading">
                <div class="spinner"></div>
                <div>等待价格数据...</div>
            </div>
        </div>
    </div>

    <!-- Cordova脚本 -->
    <script type="text/javascript" src="cordova.js"></script>
    <script type="text/javascript">
        // 全局变量
        let isScanning = false;
        let updateInterval;
        let serverUrl = 'http://*************:8080'; // 替换为你的服务器IP

        // Cordova设备就绪事件
        document.addEventListener('deviceready', onDeviceReady, false);

        function onDeviceReady() {
            console.log('Cordova设备就绪');
            // 设备就绪后的初始化
            initializeApp();
        }

        // 初始化应用
        function initializeApp() {
            console.log('初始化应用');
            // 尝试加载初始数据
            loadData();
        }

        // 启动扫描
        async function startScan() {
            try {
                updateStatus('正在启动...', 'status-warning');

                const response = await fetch(`${serverUrl}/api/start_scan`);
                const result = await response.json();

                if (result.status === 'started' || result.status === 'already_running') {
                    isScanning = true;
                    updateStatus('扫描中', 'status-running');
                    document.getElementById('startBtn').disabled = true;
                    document.getElementById('stopBtn').disabled = false;

                    // 立即加载数据并开始定期更新
                    loadData();
                    updateInterval = setInterval(loadData, 5000);
                } else {
                    throw new Error('启动失败');
                }
            } catch (error) {
                updateStatus('启动失败', 'status-error');
                if (navigator.notification) {
                    navigator.notification.alert('启动失败: ' + error.message, null, '错误', '确定');
                } else {
                    alert('启动失败: ' + error.message);
                }
            }
        }

        // 停止扫描
        async function stopScan() {
            try {
                const response = await fetch(`${serverUrl}/api/stop_scan`);
                const result = await response.json();

                if (result.status === 'stopped') {
                    isScanning = false;
                    updateStatus('已停止', '');
                    document.getElementById('startBtn').disabled = false;
                    document.getElementById('stopBtn').disabled = true;

                    if (updateInterval) {
                        clearInterval(updateInterval);
                    }
                }
            } catch (error) {
                if (navigator.notification) {
                    navigator.notification.alert('停止失败: ' + error.message, null, '错误', '确定');
                } else {
                    alert('停止失败: ' + error.message);
                }
            }
        }

        // 更新状态
        function updateStatus(text, className) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = text;
            statusDiv.className = 'status ' + className;
        }

        // 加载数据
        async function loadData() {
            try {
                const response = await fetch(`${serverUrl}/api/current_data`);
                const data = await response.json();

                if (data && (data.prices || data.arbitrage)) {
                    updateData(data);
                }
            } catch (error) {
                console.error('数据加载失败:', error);
            }
        }

        // 更新数据
        function updateData(data) {
            // 更新统计
            const cryptoCount = Object.keys(data.prices || {}).length;
            const maxArbitrage = getMaxArbitragePercentage(data.arbitrage);

            document.getElementById('cryptoCount').textContent = cryptoCount;
            document.getElementById('maxArbitrage').textContent = maxArbitrage + '%';

            // 更新最佳套利机会
            updateBestArbitrage(data.arbitrage);

            // 更新套利列表
            updateArbitrageList(data.arbitrage);

            // 更新价格网格
            updatePriceGrid(data.prices);
        }

        // 更新最佳套利机会
        function updateBestArbitrage(arbitrageData) {
            const div = document.getElementById('bestArbitrage');

            if (!arbitrageData || Object.keys(arbitrageData).length === 0) {
                div.innerHTML = '<div class="loading"><div class="spinner"></div><div>暂无套利数据</div></div>';
                return;
            }

            const sorted = Object.entries(arbitrageData)
                .sort((a, b) => b[1].arbitrage.percentage_diff - a[1].arbitrage.percentage_diff);

            if (sorted.length > 0) {
                const [crypto, data] = sorted[0];
                const arb = data.arbitrage;

                div.innerHTML = `
                    <div class="best-arbitrage">
                        <h4>🏆 ${data.info.name} (${crypto})</h4>
                        <div class="arbitrage-grid">
                            <div class="arbitrage-item">
                                <h4>${arb.percentage_diff.toFixed(3)}%</h4>
                                <small>套利空间</small>
                            </div>
                            <div class="arbitrage-item">
                                <h4>$${arb.price_diff.toFixed(4)}</h4>
                                <small>价差利润</small>
                            </div>
                        </div>
                        <div style="font-size: 0.95rem; margin-top: 15px; line-height: 1.4;">
                            <strong>买入:</strong> ${arb.min_exchange} $${arb.min_price.toFixed(4)}<br>
                            <strong>卖出:</strong> ${arb.max_exchange} $${arb.max_price.toFixed(4)}
                        </div>
                    </div>
                `;
            }
        }

        // 更新套利列表
        function updateArbitrageList(arbitrageData) {
            const div = document.getElementById('arbitrageList');

            if (!arbitrageData || Object.keys(arbitrageData).length === 0) {
                div.innerHTML = '<div class="loading"><div class="spinner"></div><div>暂无套利数据</div></div>';
                return;
            }

            const sorted = Object.entries(arbitrageData)
                .sort((a, b) => b[1].arbitrage.percentage_diff - a[1].arbitrage.percentage_diff);

            let html = '';
            sorted.slice(0, 5).forEach(([crypto, data], index) => {
                const arb = data.arbitrage;
                const rank = index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : `${index + 1}.`;

                html += `
                    <div class="arbitrage-row">
                        <div class="arbitrage-info">
                            <div style="font-weight: 700; margin-bottom: 4px;">${rank} ${data.info.name}</div>
                            <div style="font-size: 0.85rem; opacity: 0.8;">${crypto}</div>
                        </div>
                        <div class="arbitrage-profit">
                            <div style="color: #ffc107; font-size: 1.2rem;">${arb.percentage_diff.toFixed(3)}%</div>
                            <div style="font-size: 0.85rem;">$${arb.price_diff.toFixed(4)}</div>
                        </div>
                    </div>
                `;
            });

            div.innerHTML = html;
        }

        // 更新价格网格
        function updatePriceGrid(pricesData) {
            const div = document.getElementById('priceGrid');

            if (!pricesData || Object.keys(pricesData).length === 0) {
                div.innerHTML = '<div class="loading"><div class="spinner"></div><div>暂无价格数据</div></div>';
                return;
            }

            let html = '<div class="price-grid">';
            Object.entries(pricesData).forEach(([crypto, prices]) => {
                const priceCount = Object.keys(prices).length;
                html += `
                    <div class="price-item">
                        <div style="font-weight: 700; margin-bottom: 8px; font-size: 1.1rem;">${crypto}</div>
                        <div style="font-size: 0.85rem; opacity: 0.8;">${priceCount} 个交易所</div>
                    </div>
                `;
            });
            html += '</div>';

            div.innerHTML = html;
        }

        // 获取最大套利百分比
        function getMaxArbitragePercentage(arbitrageData) {
            if (!arbitrageData || Object.keys(arbitrageData).length === 0) {
                return '0.00';
            }

            const maxPercentage = Math.max(
                ...Object.values(arbitrageData).map(data => data.arbitrage.percentage_diff)
            );

            return maxPercentage.toFixed(3);
        }

        // 如果不是Cordova环境，直接初始化
        if (!window.cordova) {
            document.addEventListener('DOMContentLoaded', initializeApp);
        }
    </script>
</body>
</html>
