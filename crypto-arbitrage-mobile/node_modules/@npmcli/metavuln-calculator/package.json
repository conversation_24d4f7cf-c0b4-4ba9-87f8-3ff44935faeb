{"name": "@npmcli/metavuln-calculator", "version": "5.0.1", "main": "lib/index.js", "files": ["bin/", "lib/"], "description": "Calculate meta-vulnerabilities from package security advisories", "repository": {"type": "git", "url": "https://github.com/npm/metavuln-calculator.git"}, "author": "GitHub Inc.", "license": "ISC", "scripts": {"test": "tap", "posttest": "npm run lint", "snap": "tap", "postsnap": "npm run lint", "eslint": "eslint", "lint": "eslint \"**/*.js\"", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force"}, "tap": {"check-coverage": true, "coverage-map": "map.js", "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "devDependencies": {"@npmcli/eslint-config": "^4.0.0", "@npmcli/template-oss": "4.13.0", "require-inject": "^1.4.4", "tap": "^16.0.1"}, "dependencies": {"cacache": "^17.0.0", "json-parse-even-better-errors": "^3.0.0", "pacote": "^15.0.0", "semver": "^7.3.5"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.13.0", "publish": "true"}}