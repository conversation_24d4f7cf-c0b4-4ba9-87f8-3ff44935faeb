{"name": "@sigstore/sign", "version": "1.0.0", "description": "Sigstore signing library", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"clean": "shx rm -rf dist *.tsbuildinfo", "build": "tsc --build", "test": "jest"}, "files": ["dist"], "author": "<EMAIL>", "license": "Apache-2.0", "repository": {"type": "git", "url": "git+https://github.com/sigstore/sigstore-js.git"}, "bugs": {"url": "https://github.com/sigstore/sigstore-js/issues"}, "homepage": "https://github.com/sigstore/sigstore-js/tree/main/packages/sign#readme", "publishConfig": {"provenance": true}, "devDependencies": {"@sigstore/jest": "^0.0.0", "@sigstore/mock": "^0.2.0", "@sigstore/rekor-types": "^1.0.0", "@types/make-fetch-happen": "^10.0.0"}, "dependencies": {"@sigstore/bundle": "^1.1.0", "@sigstore/protobuf-specs": "^0.2.0", "make-fetch-happen": "^11.0.1"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}