Synopsis

    cordova-cli create <PATH> [ID [NAME]] [options]

Create a Cordova project

    PATH ......................... Where to create the project
    ID ........................... Reverse-domain-style package name - used in <widget id>
    NAME ......................... Human readable name

Options

    --template=<PATH|NPM PACKAGE|GIT URL> ... use a custom template located locally, in NPM, or GitHub.

Example
    cordova-cli create myapp com.mycompany.myteam.myapp MyApp
