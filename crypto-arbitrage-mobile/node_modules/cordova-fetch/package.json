{"name": "cordova-fetch", "version": "4.0.0", "description": "Apache Cordova fetch module. Fetches from git and npm.", "main": "index.js", "repository": "github:apache/cordova-fetch", "bugs": "https://github.com/apache/cordova-fetch/issues", "keywords": ["<PERSON><PERSON>", "fetch", "apache", "ecosystem:cordova", "cordova:tool"], "author": "Apache Software Foundation", "license": "Apache-2.0", "dependencies": {"@npmcli/arborist": "^6.2.5", "cordova-common": "^5.0.0", "execa": "^5.1.1", "fs-extra": "^11.1.1", "npm-package-arg": "^10.1.0", "pacote": "^15.1.1", "pify": "^5.0.0", "resolve": "^1.22.1", "semver": "^7.3.8", "which": "^3.0.0"}, "devDependencies": {"@cordova/eslint-config": "^5.0.0", "file-url": "^3.0.0", "jasmine": "^4.6.0", "nyc": "^15.1.0"}, "scripts": {"test": "npm run lint && npm run test:coverage", "test:coverage": "nyc jasmine", "lint": "eslint --ignore-path .gitignore ."}, "engines": {"node": ">=16.13.0", "npm": ">=8.1.0"}, "nyc": {"include": ["index.js"], "reporter": ["lcov", "text"]}}