#!/bin/bash

# 🚀 Cordova APK 自动构建脚本
# 为加密货币套利监控应用构建Android APK

echo "🚀 开始构建 Crypto Arbitrage Monitor APK..."
echo "=================================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目配置
PROJECT_DIR="crypto-arbitrage-mobile"
APP_NAME="Crypto Arbitrage Monitor"

# 检查是否在正确的目录
if [ ! -d "$PROJECT_DIR" ]; then
    echo -e "${RED}❌ 错误: 找不到项目目录 $PROJECT_DIR${NC}"
    echo "请确保在正确的目录中运行此脚本"
    exit 1
fi

cd "$PROJECT_DIR"

echo -e "${BLUE}📋 检查环境依赖...${NC}"

# 检查Node.js
if ! command -v node &> /dev/null; then
    echo -e "${RED}❌ Node.js 未安装${NC}"
    echo -e "${YELLOW}💡 请安装 Node.js: https://nodejs.org${NC}"
    echo -e "${YELLOW}   或使用 Homebrew: brew install node${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Node.js 版本: $(node --version)${NC}"

# 检查npm
if ! command -v npm &> /dev/null; then
    echo -e "${RED}❌ npm 未安装${NC}"
    exit 1
fi

echo -e "${GREEN}✅ npm 版本: $(npm --version)${NC}"

# 安装或检查Cordova
if ! command -v cordova &> /dev/null; then
    echo -e "${YELLOW}📦 安装 Cordova...${NC}"
    npm install -g cordova
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ Cordova 安装失败${NC}"
        exit 1
    fi
fi

echo -e "${GREEN}✅ Cordova 版本: $(cordova --version)${NC}"

# 检查Java
if ! command -v java &> /dev/null; then
    echo -e "${YELLOW}⚠️  Java 未安装，可能影响Android构建${NC}"
    echo -e "${YELLOW}💡 请安装 Java JDK 8 或更高版本${NC}"
fi

# 检查Android环境
echo -e "${BLUE}🤖 检查 Android 环境...${NC}"

if [ -z "$ANDROID_HOME" ]; then
    echo -e "${YELLOW}⚠️  ANDROID_HOME 环境变量未设置${NC}"
    echo -e "${YELLOW}💡 请安装 Android Studio 并设置 ANDROID_HOME${NC}"
    
    # 尝试常见的Android SDK路径
    POSSIBLE_PATHS=(
        "$HOME/Library/Android/sdk"
        "$HOME/Android/Sdk"
        "/usr/local/android-sdk"
    )
    
    for path in "${POSSIBLE_PATHS[@]}"; do
        if [ -d "$path" ]; then
            echo -e "${YELLOW}🔍 发现可能的 Android SDK 路径: $path${NC}"
            export ANDROID_HOME="$path"
            export PATH="$PATH:$ANDROID_HOME/tools:$ANDROID_HOME/platform-tools"
            break
        fi
    done
fi

if [ -n "$ANDROID_HOME" ]; then
    echo -e "${GREEN}✅ ANDROID_HOME: $ANDROID_HOME${NC}"
else
    echo -e "${YELLOW}⚠️  无法找到 Android SDK，将尝试继续构建${NC}"
fi

# 安装项目依赖
echo -e "${BLUE}📦 安装项目依赖...${NC}"

if [ ! -f "package.json" ]; then
    echo -e "${YELLOW}⚠️  package.json 不存在，跳过 npm install${NC}"
else
    npm install
    if [ $? -ne 0 ]; then
        echo -e "${YELLOW}⚠️  npm install 失败，继续尝试构建${NC}"
    fi
fi

# 检查平台
echo -e "${BLUE}📱 检查 Cordova 平台...${NC}"

if [ ! -d "platforms/android" ]; then
    echo -e "${YELLOW}📱 添加 Android 平台...${NC}"
    cordova platform add android
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ 添加 Android 平台失败${NC}"
        echo -e "${YELLOW}💡 请确保已正确安装 Android SDK${NC}"
        exit 1
    fi
else
    echo -e "${GREEN}✅ Android 平台已存在${NC}"
fi

# 检查插件
echo -e "${BLUE}🔌 检查 Cordova 插件...${NC}"

REQUIRED_PLUGINS=(
    "cordova-plugin-whitelist"
    "cordova-plugin-statusbar"
    "cordova-plugin-device"
    "cordova-plugin-splashscreen"
    "cordova-plugin-network-information"
)

for plugin in "${REQUIRED_PLUGINS[@]}"; do
    if ! cordova plugin list | grep -q "$plugin"; then
        echo -e "${YELLOW}🔌 安装插件: $plugin${NC}"
        cordova plugin add "$plugin"
    else
        echo -e "${GREEN}✅ 插件已安装: $plugin${NC}"
    fi
done

# 检查requirements
echo -e "${BLUE}🔍 检查构建要求...${NC}"
cordova requirements

# 构建APK
echo -e "${BLUE}🔨 开始构建 APK...${NC}"
echo "=================================================="

# 清理之前的构建
echo -e "${YELLOW}🧹 清理之前的构建...${NC}"
cordova clean android

# 准备构建
echo -e "${YELLOW}⚙️  准备构建...${NC}"
cordova prepare android

# 构建调试版本
echo -e "${YELLOW}🔨 构建调试版本...${NC}"
cordova build android --debug

BUILD_STATUS=$?

if [ $BUILD_STATUS -eq 0 ]; then
    echo ""
    echo "=================================================="
    echo -e "${GREEN}🎉 APK 构建成功！${NC}"
    echo "=================================================="
    echo ""
    
    # 查找APK文件
    APK_DEBUG="platforms/android/app/build/outputs/apk/debug/app-debug.apk"
    
    if [ -f "$APK_DEBUG" ]; then
        APK_SIZE=$(du -h "$APK_DEBUG" | cut -f1)
        echo -e "${GREEN}📱 调试版 APK:${NC}"
        echo -e "   文件: $APK_DEBUG"
        echo -e "   大小: $APK_SIZE"
        echo ""
        
        # 复制到项目根目录
        cp "$APK_DEBUG" "../crypto-arbitrage-debug.apk"
        echo -e "${GREEN}📋 APK 已复制到: crypto-arbitrage-debug.apk${NC}"
    fi
    
    echo -e "${BLUE}📋 安装说明:${NC}"
    echo "1. 将 APK 文件传输到 Android 设备"
    echo "2. 在设备设置中启用 '未知来源' 或 '允许安装未知应用'"
    echo "3. 点击 APK 文件进行安装"
    echo ""
    
    echo -e "${BLUE}🔧 构建发布版本 (可选):${NC}"
    echo "cordova build android --release"
    echo ""
    
    echo -e "${BLUE}🌐 服务器配置:${NC}"
    echo "请确保在 www/index.html 中设置正确的服务器IP地址"
    echo "当前设置: serverUrl = 'http://*************:8080'"
    echo ""
    
    echo -e "${GREEN}✨ 构建完成！享受你的加密货币套利监控应用！${NC}"
    
else
    echo ""
    echo "=================================================="
    echo -e "${RED}❌ APK 构建失败${NC}"
    echo "=================================================="
    echo ""
    echo -e "${YELLOW}🔍 常见问题解决方案:${NC}"
    echo "1. 确保已安装 Android Studio 和 Android SDK"
    echo "2. 设置 ANDROID_HOME 环境变量"
    echo "3. 确保 Java JDK 已正确安装"
    echo "4. 运行 'cordova requirements' 检查环境"
    echo "5. 检查网络连接，确保能下载依赖"
    echo ""
    echo -e "${YELLOW}💡 详细错误信息请查看上方的构建日志${NC}"
    echo ""
    exit 1
fi
