#!/bin/bash

# 创建应用图标脚本
# 使用ImageMagick创建不同尺寸的应用图标

echo "🎨 创建应用图标..."

PROJECT_DIR="crypto-arbitrage-mobile"
ICON_DIR="$PROJECT_DIR/www/img"

# 创建图标目录
mkdir -p "$ICON_DIR"

# 检查ImageMagick
if ! command -v convert &> /dev/null; then
    echo "⚠️  ImageMagick 未安装，将创建简单的占位符图标"
    
    # 创建简单的HTML图标占位符
    cat > "$ICON_DIR/icon-192.png.html" << 'EOF'
<!-- 这是一个图标占位符文件 -->
<!-- 请将真实的PNG图标文件重命名为对应的文件名 -->
<!-- 或者安装ImageMagick来自动生成图标: brew install imagemagick -->

需要的图标尺寸:
- icon-36.png (36x36)
- icon-48.png (48x48) 
- icon-72.png (72x72)
- icon-96.png (96x96)
- icon-144.png (144x144)
- icon-192.png (192x192)

建议使用在线图标生成器:
- https://icon.kitchen/
- https://www.favicon-generator.org/
- https://realfavicongenerator.net/
EOF

    echo "📝 已创建图标说明文件: $ICON_DIR/icon-192.png.html"
    echo "💡 请手动添加图标文件或安装ImageMagick自动生成"
    
else
    echo "✅ 使用ImageMagick生成图标..."
    
    # 创建基础图标 (192x192)
    convert -size 192x192 xc:"#667eea" \
        -fill white -gravity center \
        -pointsize 80 -font Arial-Bold \
        -annotate +0+0 "₿" \
        "$ICON_DIR/icon-192.png"
    
    # 生成不同尺寸
    convert "$ICON_DIR/icon-192.png" -resize 144x144 "$ICON_DIR/icon-144.png"
    convert "$ICON_DIR/icon-192.png" -resize 96x96 "$ICON_DIR/icon-96.png"
    convert "$ICON_DIR/icon-192.png" -resize 72x72 "$ICON_DIR/icon-72.png"
    convert "$ICON_DIR/icon-192.png" -resize 48x48 "$ICON_DIR/icon-48.png"
    convert "$ICON_DIR/icon-192.png" -resize 36x36 "$ICON_DIR/icon-36.png"
    
    echo "✅ 图标生成完成！"
    echo "📁 图标位置: $ICON_DIR/"
    ls -la "$ICON_DIR/"*.png
fi

echo "🎨 图标创建完成！"
