#!/usr/bin/env python3
"""
自动套利扫描器 - 扫描10种主流加密货币，找出最大套利机会
"""

import requests
import websocket
import json
import time
import threading
from datetime import datetime
import pytz
import logging
from collections import defaultdict
import ssl

# 配置日志
logging.basicConfig(
    level=logging.WARNING,  # 只显示警告和错误，减少日志噪音
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class AutoArbitrageScanner:
    def __init__(self):
        self.local_tz = pytz.timezone('Asia/Shanghai')
        self.running = False
        
        # 10种主流加密货币配置
        self.cryptocurrencies = {
            "BTC": {"name": "比特币", "base": "USDT"},
            "ETH": {"name": "以太坊", "base": "USDT"},
            "BNB": {"name": "币安币", "base": "USDT"},
            "XRP": {"name": "瑞波币", "base": "USDT"},
            "ADA": {"name": "卡尔达诺", "base": "USDT"},
            "DOGE": {"name": "狗狗币", "base": "USDT"},
            "SOL": {"name": "索拉纳", "base": "USDT"},
            "TRX": {"name": "波场", "base": "USDT"},
            "DOT": {"name": "波卡", "base": "USDT"},
            "AVAX": {"name": "雪崩", "base": "USDT"}
        }
        
        # 存储所有货币的价格数据
        self.all_prices = {}
        self.price_lock = threading.Lock()
        
        # 套利机会记录
        self.arbitrage_opportunities = {}
        
        # 当前扫描的货币
        self.current_crypto = None
        self.scan_duration = 10  # 每种货币扫描10秒
        
    def get_exchange_config(self, symbol, base_currency):
        """获取交易所配置"""
        return {
            'Binance': {'type': 'rest', 'symbol_format': f'{symbol}{base_currency}'},
            'OKX': {'type': 'rest', 'symbol_format': f'{symbol}-{base_currency}'},
            'Bybit': {'type': 'rest', 'symbol_format': f'{symbol}{base_currency}'},
            'MEXC': {'type': 'rest', 'symbol_format': f'{symbol}{base_currency}'},
            'KuCoin': {'type': 'rest', 'symbol_format': f'{symbol}-{base_currency}'},
            'Gate.io': {'type': 'rest', 'symbol_format': f'{symbol}_{base_currency}'},
            'Coinbase': {'type': 'rest', 'symbol_format': f'{symbol}-USD'},
            'Kraken': {'type': 'rest', 'symbol_format': f'{symbol}/USD'},
            'Gemini': {'type': 'rest', 'symbol_format': f'{symbol}USD'}
        }
    
    def fetch_price_from_exchange(self, exchange, symbol_format, crypto_symbol):
        """从单个交易所获取价格"""
        try:
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
            }
            
            if exchange == 'Binance':
                url = f"https://api.binance.com/api/v3/ticker/price?symbol={symbol_format}"
                response = requests.get(url, headers=headers, timeout=3)
                if response.status_code == 200:
                    data = response.json()
                    return float(data['price'])
                    
            elif exchange == 'OKX':
                url = f"https://www.okx.com/api/v5/market/ticker?instId={symbol_format}"
                response = requests.get(url, headers=headers, timeout=3)
                if response.status_code == 200:
                    data = response.json()
                    if data.get('data'):
                        return float(data['data'][0]['last'])
                        
            elif exchange == 'Bybit':
                url = f"https://api.bybit.com/v5/market/tickers?category=spot&symbol={symbol_format}"
                response = requests.get(url, headers=headers, timeout=3)
                if response.status_code == 200:
                    data = response.json()
                    if data.get('result', {}).get('list'):
                        return float(data['result']['list'][0]['lastPrice'])
                        
            elif exchange == 'MEXC':
                url = f"https://api.mexc.com/api/v3/ticker/price?symbol={symbol_format}"
                response = requests.get(url, headers=headers, timeout=3)
                if response.status_code == 200:
                    data = response.json()
                    return float(data['price'])
                    
            elif exchange == 'KuCoin':
                url = f"https://api.kucoin.com/api/v1/market/orderbook/level1?symbol={symbol_format}"
                response = requests.get(url, headers=headers, timeout=3)
                if response.status_code == 200:
                    data = response.json()
                    if data.get('code') == '200000' and 'data' in data:
                        return float(data['data']['price'])
                        
            elif exchange == 'Gate.io':
                url = f"https://api.gateio.ws/api/v4/spot/tickers?currency_pair={symbol_format}"
                response = requests.get(url, headers=headers, timeout=3)
                if response.status_code == 200:
                    data = response.json()
                    if data:
                        return float(data[0]['last'])
                        
            elif exchange == 'Coinbase':
                # 使用公开API获取价格
                url = f"https://api.coinbase.com/v2/exchange-rates?currency={crypto_symbol}"
                response = requests.get(url, headers=headers, timeout=3)
                if response.status_code == 200:
                    data = response.json()
                    if 'data' in data and 'rates' in data['data']:
                        usd_rate = data['data']['rates'].get('USD')
                        if usd_rate:
                            return float(usd_rate)
                            
            elif exchange == 'Kraken':
                # 转换symbol格式
                kraken_symbol = symbol_format.replace('/', '').replace('-', '')
                if kraken_symbol == 'BTCUSD':
                    kraken_symbol = 'XXBTZUSD'
                elif kraken_symbol == 'ETHUSD':
                    kraken_symbol = 'XETHZUSD'
                    
                url = f"https://api.kraken.com/0/public/Ticker?pair={kraken_symbol}"
                response = requests.get(url, headers=headers, timeout=3)
                if response.status_code == 200:
                    data = response.json()
                    if 'result' in data and data['result']:
                        pair_data = list(data['result'].values())[0]
                        if 'c' in pair_data:
                            return float(pair_data['c'][0])
                            
            elif exchange == 'Gemini':
                url = f"https://api.gemini.com/v1/pubticker/{symbol_format.lower()}"
                response = requests.get(url, headers=headers, timeout=3)
                if response.status_code == 200:
                    data = response.json()
                    return float(data['last'])
                    
        except Exception as e:
            logging.debug(f"{exchange} 获取 {crypto_symbol} 价格失败: {str(e)}")
            return None
    
    def scan_crypto_prices(self, crypto_symbol):
        """扫描单个加密货币在所有交易所的价格"""
        crypto_info = self.cryptocurrencies[crypto_symbol]
        base_currency = crypto_info['base']
        exchanges = self.get_exchange_config(crypto_symbol, base_currency)
        
        prices = {}
        threads = []
        
        def fetch_price(exchange, config):
            price = self.fetch_price_from_exchange(
                exchange, 
                config['symbol_format'], 
                crypto_symbol
            )
            if price:
                prices[exchange] = {
                    'price': price,
                    'timestamp': datetime.now(self.local_tz),
                    'symbol': config['symbol_format']
                }
        
        # 并发获取所有交易所价格
        for exchange, config in exchanges.items():
            thread = threading.Thread(target=fetch_price, args=(exchange, config))
            thread.start()
            threads.append(thread)
        
        # 等待所有线程完成
        for thread in threads:
            thread.join(timeout=5)
        
        return prices
    
    def calculate_arbitrage(self, prices):
        """计算套利机会"""
        if len(prices) < 2:
            return None
            
        price_values = [data['price'] for data in prices.values()]
        max_price = max(price_values)
        min_price = min(price_values)
        price_diff = max_price - min_price
        percentage_diff = (price_diff / min_price) * 100
        
        # 找出最高价和最低价的交易所
        max_exchange = next(ex for ex, data in prices.items() if data['price'] == max_price)
        min_exchange = next(ex for ex, data in prices.items() if data['price'] == min_price)
        
        return {
            'max_exchange': max_exchange,
            'max_price': max_price,
            'min_exchange': min_exchange,
            'min_price': min_price,
            'price_diff': price_diff,
            'percentage_diff': percentage_diff,
            'exchange_count': len(prices)
        }
    
    def start_scanning(self):
        """开始自动扫描所有加密货币"""
        self.running = True
        
        print("🚀 自动套利扫描器启动！")
        print("="*80)
        print("正在扫描10种主流加密货币的套利机会...")
        print("="*80)
        
        while self.running:
            round_start_time = time.time()
            round_opportunities = {}
            
            for crypto_symbol, crypto_info in self.cryptocurrencies.items():
                if not self.running:
                    break
                    
                self.current_crypto = crypto_symbol
                print(f"\n🔍 正在扫描 {crypto_info['name']} ({crypto_symbol})...")
                
                # 获取价格数据
                prices = self.scan_crypto_prices(crypto_symbol)
                
                if len(prices) >= 3:  # 至少需要3个交易所的数据
                    arbitrage = self.calculate_arbitrage(prices)
                    if arbitrage:
                        round_opportunities[crypto_symbol] = {
                            'info': crypto_info,
                            'arbitrage': arbitrage,
                            'prices': prices
                        }
                        
                        print(f"   💰 发现套利机会: {arbitrage['percentage_diff']:.3f}%")
                        print(f"   📈 最高价: {arbitrage['max_exchange']} ${arbitrage['max_price']:.4f}")
                        print(f"   📉 最低价: {arbitrage['min_exchange']} ${arbitrage['min_price']:.4f}")
                        print(f"   💵 价差: ${arbitrage['price_diff']:.4f}")
                else:
                    print(f"   ❌ 数据不足 (仅获取到 {len(prices)} 个交易所数据)")
                
                time.sleep(2)  # 每种货币间隔2秒
            
            # 分析本轮最佳套利机会
            if round_opportunities:
                self.analyze_best_opportunity(round_opportunities)
            
            round_duration = time.time() - round_start_time
            print(f"\n⏱️  本轮扫描耗时: {round_duration:.1f}秒")
            print("="*80)
            
            # 等待下一轮扫描
            time.sleep(5)
    
    def analyze_best_opportunity(self, opportunities):
        """分析最佳套利机会"""
        print("\n" + "="*80)
        print("📊 本轮套利机会排行榜")
        print("="*80)
        
        # 按套利百分比排序
        sorted_opportunities = sorted(
            opportunities.items(),
            key=lambda x: x[1]['arbitrage']['percentage_diff'],
            reverse=True
        )
        
        for i, (crypto, data) in enumerate(sorted_opportunities[:5], 1):
            arb = data['arbitrage']
            info = data['info']
            
            print(f"{i}. 🏆 {info['name']} ({crypto})")
            print(f"   💎 套利空间: {arb['percentage_diff']:.3f}%")
            print(f"   🔥 买入: {arb['min_exchange']} ${arb['min_price']:.4f}")
            print(f"   💰 卖出: {arb['max_exchange']} ${arb['max_price']:.4f}")
            print(f"   💵 利润: ${arb['price_diff']:.4f}")
            print(f"   📊 数据源: {arb['exchange_count']} 个交易所")
            print()
        
        # 突出显示最佳机会
        if sorted_opportunities:
            best = sorted_opportunities[0]
            best_crypto = best[0]
            best_data = best[1]
            best_arb = best_data['arbitrage']
            
            print("🎯 " + "="*76)
            print(f"🚀 最佳套利机会: {best_data['info']['name']} ({best_crypto})")
            print(f"💎 套利空间: {best_arb['percentage_diff']:.3f}%")
            print(f"📈 策略: 在 {best_arb['min_exchange']} 买入 ${best_arb['min_price']:.4f}")
            print(f"📉 策略: 在 {best_arb['max_exchange']} 卖出 ${best_arb['max_price']:.4f}")
            print(f"💰 预期利润: ${best_arb['price_diff']:.4f} ({best_arb['percentage_diff']:.3f}%)")
            print("🎯 " + "="*76)
    
    def stop_scanning(self):
        """停止扫描"""
        self.running = False
        print("\n⏹️  扫描已停止")

def main():
    """主函数"""
    scanner = AutoArbitrageScanner()
    
    try:
        scanner.start_scanning()
    except KeyboardInterrupt:
        scanner.stop_scanning()
        print("\n👋 程序已退出")

if __name__ == "__main__":
    main()
