#!/bin/bash

# 创建 macOS 应用程序包脚本
# 将 Python 脚本打包成 .app 格式

echo "🚀 创建 macOS 应用程序包..."

# 应用程序名称
APP_NAME="CryptoArbitrageMonitor"
APP_DIR="${APP_NAME}.app"

# 清理旧的应用程序包
if [ -d "$APP_DIR" ]; then
    echo "🗑️  清理旧的应用程序包..."
    rm -rf "$APP_DIR"
fi

# 创建应用程序包结构
echo "📁 创建应用程序包结构..."
mkdir -p "$APP_DIR/Contents/MacOS"
mkdir -p "$APP_DIR/Contents/Resources"

# 创建 Info.plist 文件
echo "📝 创建 Info.plist..."
cat > "$APP_DIR/Contents/Info.plist" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>CFBundleExecutable</key>
    <string>CryptoArbitrageMonitor</string>
    <key>CFBundleIdentifier</key>
    <string>com.crypto.arbitrage.monitor</string>
    <key>CFBundleName</key>
    <string>Crypto Arbitrage Monitor</string>
    <key>CFBundleDisplayName</key>
    <string>Crypto Arbitrage Monitor</string>
    <key>CFBundleVersion</key>
    <string>1.0.0</string>
    <key>CFBundleShortVersionString</key>
    <string>1.0.0</string>
    <key>CFBundlePackageType</key>
    <string>APPL</string>
    <key>CFBundleSignature</key>
    <string>????</string>
    <key>LSMinimumSystemVersion</key>
    <string>10.13.0</string>
    <key>NSHighResolutionCapable</key>
    <true/>
    <key>LSUIElement</key>
    <false/>
    <key>NSAppTransportSecurity</key>
    <dict>
        <key>NSAllowsArbitraryLoads</key>
        <true/>
    </dict>
</dict>
</plist>
EOF

# 创建启动脚本
echo "🔧 创建启动脚本..."
cat > "$APP_DIR/Contents/MacOS/CryptoArbitrageMonitor" << 'EOF'
#!/bin/bash

# 获取应用程序包路径
APP_PATH="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
RESOURCES_PATH="$APP_PATH/../Resources"

# 设置工作目录
cd "$RESOURCES_PATH"

# 检查 Python 是否可用
if command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
elif command -v python &> /dev/null; then
    PYTHON_CMD="python"
else
    osascript -e 'display dialog "错误: 未找到 Python 解释器\n\n请安装 Python 3.6 或更高版本" buttons {"确定"} default button "确定" with icon stop'
    exit 1
fi

# 检查并安装依赖
echo "🔧 检查依赖..."
$PYTHON_CMD -c "import requests, pytz" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "📦 安装依赖..."
    $PYTHON_CMD -m pip install requests pytz websocket-client
fi

# 启动应用程序
echo "🚀 启动加密货币套利监控..."

# 在终端中运行
osascript << APPLESCRIPT
tell application "Terminal"
    activate
    do script "cd '$RESOURCES_PATH' && $PYTHON_CMD crypto_monitor_standalone.py"
end tell
APPLESCRIPT

EOF

# 设置执行权限
chmod +x "$APP_DIR/Contents/MacOS/CryptoArbitrageMonitor"

# 复制 Python 脚本到 Resources 目录
echo "📋 复制应用程序文件..."
cp crypto_monitor_standalone.py "$APP_DIR/Contents/Resources/"

# 创建图标文件 (可选)
echo "🎨 创建应用程序图标..."
cat > "$APP_DIR/Contents/Resources/app_icon.py" << 'EOF'
#!/usr/bin/env python3
"""
创建简单的应用程序图标
"""
try:
    from PIL import Image, ImageDraw, ImageFont
    import os
    
    # 创建 512x512 的图标
    size = 512
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # 绘制背景圆形
    margin = 20
    draw.ellipse([margin, margin, size-margin, size-margin], 
                fill=(34, 139, 34, 255), outline=(0, 100, 0, 255), width=8)
    
    # 绘制货币符号
    try:
        font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 200)
    except:
        font = ImageFont.load_default()
    
    text = "₿"
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    
    x = (size - text_width) // 2
    y = (size - text_height) // 2 - 20
    
    draw.text((x, y), text, fill=(255, 255, 255, 255), font=font)
    
    # 保存为 PNG
    img.save('app_icon.png')
    print("✅ 图标创建成功")
    
except ImportError:
    print("⚠️  PIL 未安装，跳过图标创建")
except Exception as e:
    print(f"⚠️  图标创建失败: {e}")
EOF

# 尝试创建图标
cd "$APP_DIR/Contents/Resources"
python3 app_icon.py 2>/dev/null || echo "⚠️  跳过图标创建"
cd - > /dev/null

# 完成
echo ""
echo "✅ macOS 应用程序包创建完成！"
echo "📱 应用程序: $APP_DIR"
echo ""
echo "🚀 使用方法:"
echo "  1. 双击 $APP_DIR 启动应用"
echo "  2. 或在终端运行: open $APP_DIR"
echo ""
echo "📝 注意事项:"
echo "  • 首次运行可能需要在系统偏好设置中允许运行"
echo "  • 应用程序会在终端中运行"
echo "  • 确保已安装 Python 3.6 或更高版本"
echo ""
echo "🎉 享受加密货币套利监控！"
