#!/bin/bash

# 增强版 macOS 应用程序包创建脚本
# 将 Python 脚本打包成专业的 .app 格式

echo "🚀 创建增强版 macOS 应用程序包..."
echo "=" * 50

# 应用程序配置
APP_NAME="CryptoArbitrageMonitor"
APP_DIR="${APP_NAME}.app"
APP_VERSION="2.0.0"
BUNDLE_ID="com.crypto.arbitrage.monitor"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印彩色消息
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# 检查依赖
check_dependencies() {
    print_info "检查系统依赖..."

    if ! command -v python3 &> /dev/null; then
        print_error "Python 3 未安装，请先安装 Python 3"
        exit 1
    fi

    PYTHON_VERSION=$(python3 --version | cut -d' ' -f2)
    print_status "Python 版本: $PYTHON_VERSION"
}

# 清理旧的应用程序包
cleanup_old_app() {
    if [ -d "$APP_DIR" ]; then
        print_warning "清理旧的应用程序包..."
        rm -rf "$APP_DIR"
    fi
}

# 创建应用程序包结构
create_app_structure() {
    print_info "创建应用程序包结构..."
    mkdir -p "$APP_DIR/Contents/MacOS"
    mkdir -p "$APP_DIR/Contents/Resources"
    mkdir -p "$APP_DIR/Contents/Resources/Scripts"
    print_status "应用程序包结构创建完成"
}

# 创建增强版 Info.plist 文件
create_info_plist() {
    print_info "创建 Info.plist 配置文件..."
    cat > "$APP_DIR/Contents/Info.plist" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>CFBundleExecutable</key>
    <string>CryptoArbitrageMonitor</string>
    <key>CFBundleIdentifier</key>
    <string>$BUNDLE_ID</string>
    <key>CFBundleName</key>
    <string>Crypto Arbitrage Monitor</string>
    <key>CFBundleDisplayName</key>
    <string>加密货币套利监控</string>
    <key>CFBundleVersion</key>
    <string>$APP_VERSION</string>
    <key>CFBundleShortVersionString</key>
    <string>$APP_VERSION</string>
    <key>CFBundleInfoDictionaryVersion</key>
    <string>6.0</string>
    <key>CFBundlePackageType</key>
    <string>APPL</string>
    <key>CFBundleSignature</key>
    <string>????</string>
    <key>LSMinimumSystemVersion</key>
    <string>10.13.0</string>
    <key>NSHighResolutionCapable</key>
    <true/>
    <key>LSUIElement</key>
    <false/>
    <key>NSAppTransportSecurity</key>
    <dict>
        <key>NSAllowsArbitraryLoads</key>
        <true/>
    </dict>
    <key>NSAppleEventsUsageDescription</key>
    <string>此应用需要访问终端来运行加密货币监控程序</string>
    <key>NSNetworkVolumesUsageDescription</key>
    <string>此应用需要网络访问来获取加密货币价格数据</string>
    <key>CFBundleDocumentTypes</key>
    <array>
        <dict>
            <key>CFBundleTypeRole</key>
            <string>Viewer</string>
            <key>LSHandlerRank</key>
            <string>Owner</string>
        </dict>
    </array>
</dict>
</plist>
EOF
    print_status "Info.plist 创建完成"
}

# 创建增强版启动脚本
create_launcher_script() {
    print_info "创建启动脚本..."
    cat > "$APP_DIR/Contents/MacOS/CryptoArbitrageMonitor" << 'EOF'
#!/bin/bash

# 增强版启动脚本 - 加密货币套利监控
# 版本: 2.0.0

# 获取应用程序包路径
APP_PATH="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
RESOURCES_PATH="$APP_PATH/../Resources"
SCRIPTS_PATH="$RESOURCES_PATH/Scripts"

# 设置工作目录
cd "$RESOURCES_PATH"

# 日志文件
LOG_FILE="$RESOURCES_PATH/app.log"

# 记录日志
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$LOG_FILE"
}

log_message "应用程序启动"

# 检查 Python 是否可用
check_python() {
    if command -v python3 &> /dev/null; then
        PYTHON_CMD="python3"
        PYTHON_VERSION=$(python3 --version 2>&1)
        log_message "找到 Python: $PYTHON_VERSION"
    elif command -v python &> /dev/null; then
        PYTHON_CMD="python"
        PYTHON_VERSION=$(python --version 2>&1)
        log_message "找到 Python: $PYTHON_VERSION"
    else
        log_message "错误: 未找到 Python 解释器"
        osascript -e 'display dialog "❌ 错误: 未找到 Python 解释器\n\n请安装 Python 3.6 或更高版本\n\n推荐从 python.org 下载安装" buttons {"确定"} default button "确定" with icon stop with title "加密货币套利监控"'
        exit 1
    fi
}

# 检查并安装依赖
install_dependencies() {
    log_message "检查 Python 依赖..."

    # 检查依赖是否已安装
    $PYTHON_CMD -c "import requests, pytz, websocket" 2>/dev/null
    if [ $? -ne 0 ]; then
        log_message "安装缺失的依赖..."

        # 显示安装进度对话框
        osascript -e 'display dialog "📦 正在安装必要的依赖包...\n\n• requests (HTTP 请求)\n• pytz (时区处理)\n• websocket-client (WebSocket 连接)\n\n请稍候..." buttons {"确定"} default button "确定" with icon note with title "加密货币套利监控" giving up after 3'

        # 安装依赖
        $PYTHON_CMD -m pip install requests pytz websocket-client >> "$LOG_FILE" 2>&1

        if [ $? -eq 0 ]; then
            log_message "依赖安装成功"
        else
            log_message "依赖安装失败"
            osascript -e 'display dialog "❌ 依赖安装失败\n\n请检查网络连接或手动安装:\npip3 install requests pytz websocket-client" buttons {"确定"} default button "确定" with icon stop with title "加密货币套利监控"'
            exit 1
        fi
    else
        log_message "所有依赖已安装"
    fi
}

# 启动主程序
start_main_program() {
    log_message "启动主程序"

    # 检查主程序文件是否存在
    if [ ! -f "crypto_monitor_standalone.py" ]; then
        log_message "错误: 主程序文件不存在"
        osascript -e 'display dialog "❌ 错误: 主程序文件不存在\n\ncrypto_monitor_standalone.py 文件缺失" buttons {"确定"} default button "确定" with icon stop with title "加密货币套利监控"'
        exit 1
    fi

    # 显示启动消息
    osascript -e 'display dialog "🚀 启动加密货币套利监控系统\n\n• 支持 10 种主流加密货币\n• 监控 9 个主要交易所\n• 实时套利机会分析\n\n程序将在终端中运行..." buttons {"启动"} default button "启动" with icon note with title "加密货币套利监控" giving up after 5'

    # 在终端中运行主程序
    osascript << APPLESCRIPT
tell application "Terminal"
    activate
    set newTab to do script "cd '$RESOURCES_PATH' && echo '🎯 加密货币套利监控系统 v2.0' && echo '应用程序路径: $APP_PATH' && echo '资源路径: $RESOURCES_PATH' && echo '' && $PYTHON_CMD crypto_monitor_standalone.py"
    set custom title of newTab to "加密货币套利监控"
end tell
APPLESCRIPT

    log_message "主程序启动完成"
}

# 主执行流程
main() {
    check_python
    install_dependencies
    start_main_program
}

# 执行主函数
main

EOF

    # 设置执行权限
    chmod +x "$APP_DIR/Contents/MacOS/CryptoArbitrageMonitor"
    print_status "启动脚本创建完成"
}

# 复制应用程序文件
copy_app_files() {
    print_info "复制应用程序文件..."

    # 复制主程序
    if [ -f "crypto_monitor_standalone.py" ]; then
        cp crypto_monitor_standalone.py "$APP_DIR/Contents/Resources/"
        print_status "主程序文件复制完成"
    else
        print_error "主程序文件 crypto_monitor_standalone.py 不存在"
        exit 1
    fi

    # 复制其他相关文件
    if [ -f "multi_exchange_price_monitor.py" ]; then
        cp multi_exchange_price_monitor.py "$APP_DIR/Contents/Resources/"
        print_status "备用监控程序复制完成"
    fi

    # 复制文档文件
    for doc in "MACOS_EXECUTABLE_GUIDE.md" "TROUBLESHOOTING_GUIDE.md" "README.md"; do
        if [ -f "$doc" ]; then
            cp "$doc" "$APP_DIR/Contents/Resources/"
            print_status "文档文件 $doc 复制完成"
        fi
    done
}

# 创建应用程序图标
create_app_icon() {
    print_info "创建应用程序图标..."

    # 创建简单的图标生成脚本
    cat > "$APP_DIR/Contents/Resources/Scripts/create_icon.py" << 'EOF'
#!/usr/bin/env python3
"""
创建专业的应用程序图标
支持多种尺寸和格式
"""
import os
import sys

def create_simple_icon():
    """创建简单的文本图标"""
    try:
        # 创建 icns 文件的替代方案 - 使用 sips 命令
        icon_text = """<?xml version="1.0" encoding="UTF-8"?>
<svg width="512" height="512" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2E7D32;stop-opacity:1" />
    </linearGradient>
  </defs>
  <circle cx="256" cy="256" r="240" fill="url(#grad1)" stroke="#1B5E20" stroke-width="8"/>
  <text x="256" y="320" font-family="Arial, sans-serif" font-size="200" font-weight="bold"
        text-anchor="middle" fill="white">₿</text>
  <text x="256" y="400" font-family="Arial, sans-serif" font-size="40"
        text-anchor="middle" fill="white">ARBITRAGE</text>
</svg>"""

        with open('../app_icon.svg', 'w') as f:
            f.write(icon_text)

        print("✅ SVG 图标创建成功")
        return True

    except Exception as e:
        print(f"⚠️  图标创建失败: {e}")
        return False

def create_png_icon():
    """使用 PIL 创建 PNG 图标"""
    try:
        from PIL import Image, ImageDraw, ImageFont

        # 创建多个尺寸的图标
        sizes = [16, 32, 64, 128, 256, 512]

        for size in sizes:
            img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
            draw = ImageDraw.Draw(img)

            # 绘制背景圆形
            margin = max(2, size // 20)
            draw.ellipse([margin, margin, size-margin, size-margin],
                        fill=(76, 175, 80, 255), outline=(27, 94, 32, 255), width=max(1, size//64))

            # 绘制货币符号
            font_size = max(size//3, 12)
            try:
                font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", font_size)
            except:
                font = ImageFont.load_default()

            text = "₿"
            bbox = draw.textbbox((0, 0), text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]

            x = (size - text_width) // 2
            y = (size - text_height) // 2 - size//20

            draw.text((x, y), text, fill=(255, 255, 255, 255), font=font)

            # 保存图标
            img.save(f'../app_icon_{size}x{size}.png')

        print("✅ PNG 图标创建成功")
        return True

    except ImportError:
        print("⚠️  PIL 未安装，使用简单图标")
        return False
    except Exception as e:
        print(f"⚠️  PNG 图标创建失败: {e}")
        return False

if __name__ == "__main__":
    success = create_png_icon()
    if not success:
        create_simple_icon()
EOF

    # 运行图标创建脚本
    cd "$APP_DIR/Contents/Resources/Scripts"
    python3 create_icon.py 2>/dev/null || print_warning "图标创建失败，使用默认图标"
    cd - > /dev/null

    print_status "图标处理完成"
}

# 创建帮助文档
create_help_docs() {
    print_info "创建帮助文档..."

    # 创建使用说明
    cat > "$APP_DIR/Contents/Resources/使用说明.txt" << 'EOF'
🎯 加密货币套利监控 v2.0 使用说明

📱 应用程序功能:
• 监控 10 种主流加密货币价格
• 支持 9 个主要交易所
• 实时套利机会分析
• 自动排行榜显示

🚀 使用方法:
1. 双击应用程序图标启动
2. 程序会在终端中运行
3. 选择要监控的加密货币 (1-11)
4. 选择 11 可扫描所有货币找最佳套利机会

💡 支持的加密货币:
• BTC (比特币)
• ETH (以太坊)
• BNB (币安币)
• XRP (瑞波币)
• ADA (卡尔达诺)
• DOGE (狗狗币)
• SOL (索拉纳)
• TRX (波场)
• DOT (波卡)
• AVAX (雪崩)

🏢 支持的交易所:
• Binance (币安)
• OKX
• Bybit
• MEXC
• KuCoin
• Gemini
• Gate.io
• Coinbase
• Kraken

📝 注意事项:
• 首次运行可能需要在系统偏好设置中允许运行
• 需要稳定的网络连接
• 套利机会实时变化，请及时关注

🔧 技术支持:
如遇问题，请检查:
1. Python 是否正确安装 (python3 --version)
2. 网络连接是否正常
3. 查看应用程序日志文件 (app.log)

版本: 2.0.0
更新日期: $(date '+%Y-%m-%d')
EOF

    print_status "帮助文档创建完成"
}

# 设置应用程序权限
set_permissions() {
    print_info "设置应用程序权限..."

    # 设置执行权限
    chmod +x "$APP_DIR/Contents/MacOS/CryptoArbitrageMonitor"
    chmod -R 755 "$APP_DIR"

    # 移除扩展属性 (避免安全警告)
    xattr -cr "$APP_DIR" 2>/dev/null || true

    print_status "权限设置完成"
}

# 验证应用程序包
verify_app() {
    print_info "验证应用程序包..."

    # 检查必要文件
    local required_files=(
        "$APP_DIR/Contents/Info.plist"
        "$APP_DIR/Contents/MacOS/CryptoArbitrageMonitor"
        "$APP_DIR/Contents/Resources/crypto_monitor_standalone.py"
    )

    for file in "${required_files[@]}"; do
        if [ ! -f "$file" ]; then
            print_error "缺少必要文件: $file"
            exit 1
        fi
    done

    print_status "应用程序包验证通过"
}

# 显示完成信息
show_completion_info() {
    echo ""
    echo "🎉" "=" * 60
    print_status "macOS 应用程序包创建完成！"
    echo "🎉" "=" * 60
    echo ""

    print_info "📱 应用程序: $APP_DIR"
    print_info "📦 版本: $APP_VERSION"
    print_info "🆔 Bundle ID: $BUNDLE_ID"
    echo ""

    print_info "🚀 使用方法:"
    echo "  1. 双击 $APP_DIR 启动应用"
    echo "  2. 或在终端运行: open $APP_DIR"
    echo "  3. 或拖拽到应用程序文件夹"
    echo ""

    print_info "📝 重要提示:"
    echo "  • 首次运行可能需要在系统偏好设置中允许运行"
    echo "  • 应用程序会在终端中运行，提供最佳用户体验"
    echo "  • 确保已安装 Python 3.6 或更高版本"
    echo "  • 需要稳定的网络连接获取实时价格数据"
    echo ""

    print_info "📊 功能特色:"
    echo "  • 10种主流加密货币实时监控"
    echo "  • 9个主要交易所价格对比"
    echo "  • 智能套利机会分析和排行榜"
    echo "  • 自动依赖管理和错误处理"
    echo ""

    print_status "🎉 享受专业的加密货币套利监控体验！"
    echo ""
}

# 主执行函数
main() {
    echo "🚀 开始创建增强版 macOS 应用程序包..."
    echo ""

    check_dependencies
    cleanup_old_app
    create_app_structure
    create_info_plist
    create_launcher_script
    copy_app_files
    create_app_icon
    create_help_docs
    set_permissions
    verify_app
    show_completion_info
}

# 执行主函数
main
