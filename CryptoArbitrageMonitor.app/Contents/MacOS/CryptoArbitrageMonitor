#!/bin/bash

# 获取应用程序包路径
APP_PATH="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
RESOURCES_PATH="$APP_PATH/../Resources"

# 设置工作目录
cd "$RESOURCES_PATH"

# 检查 Python 是否可用
if command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
elif command -v python &> /dev/null; then
    PYTHON_CMD="python"
else
    osascript -e 'display dialog "错误: 未找到 Python 解释器\n\n请安装 Python 3.6 或更高版本" buttons {"确定"} default button "确定" with icon stop'
    exit 1
fi

# 检查并安装依赖
echo "🔧 检查依赖..."
$PYTHON_CMD -c "import requests, pytz" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "📦 安装依赖..."
    $PYTHON_CMD -m pip install requests pytz websocket-client
fi

# 启动应用程序
echo "🚀 启动加密货币套利监控..."

# 在终端中运行
osascript << APPLESCRIPT
tell application "Terminal"
    activate
    do script "cd '$RESOURCES_PATH' && $PYTHON_CMD crypto_monitor_standalone.py"
end tell
APPLESCRIPT

