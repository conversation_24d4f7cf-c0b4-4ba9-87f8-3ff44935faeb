#!/bin/bash

# 增强版启动脚本 - 加密货币套利监控
# 版本: 2.0.0

# 获取应用程序包路径
APP_PATH="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
RESOURCES_PATH="$APP_PATH/../Resources"
SCRIPTS_PATH="$RESOURCES_PATH/Scripts"

# 设置工作目录
cd "$RESOURCES_PATH"

# 日志文件
LOG_FILE="$RESOURCES_PATH/app.log"

# 记录日志
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$LOG_FILE"
}

log_message "应用程序启动"

# 检查 Python 是否可用
check_python() {
    if command -v python3 &> /dev/null; then
        PYTHON_CMD="python3"
        PYTHON_VERSION=$(python3 --version 2>&1)
        log_message "找到 Python: $PYTHON_VERSION"
    elif command -v python &> /dev/null; then
        PYTHON_CMD="python"
        PYTHON_VERSION=$(python --version 2>&1)
        log_message "找到 Python: $PYTHON_VERSION"
    else
        log_message "错误: 未找到 Python 解释器"
        osascript -e 'display dialog "❌ 错误: 未找到 Python 解释器\n\n请安装 Python 3.6 或更高版本\n\n推荐从 python.org 下载安装" buttons {"确定"} default button "确定" with icon stop with title "加密货币套利监控"'
        exit 1
    fi
}

# 检查并安装依赖
install_dependencies() {
    log_message "检查 Python 依赖..."

    # 检查依赖是否已安装
    $PYTHON_CMD -c "import requests, pytz, websocket" 2>/dev/null
    if [ $? -ne 0 ]; then
        log_message "安装缺失的依赖..."

        # 显示安装进度对话框
        osascript -e 'display dialog "📦 正在安装必要的依赖包...\n\n• requests (HTTP 请求)\n• pytz (时区处理)\n• websocket-client (WebSocket 连接)\n\n请稍候..." buttons {"确定"} default button "确定" with icon note with title "加密货币套利监控" giving up after 3'

        # 安装依赖
        $PYTHON_CMD -m pip install requests pytz websocket-client >> "$LOG_FILE" 2>&1

        if [ $? -eq 0 ]; then
            log_message "依赖安装成功"
        else
            log_message "依赖安装失败"
            osascript -e 'display dialog "❌ 依赖安装失败\n\n请检查网络连接或手动安装:\npip3 install requests pytz websocket-client" buttons {"确定"} default button "确定" with icon stop with title "加密货币套利监控"'
            exit 1
        fi
    else
        log_message "所有依赖已安装"
    fi
}

# 启动主程序
start_main_program() {
    log_message "启动主程序"

    # 检查主程序文件是否存在
    if [ ! -f "crypto_monitor_standalone.py" ]; then
        log_message "错误: 主程序文件不存在"
        osascript -e 'display dialog "❌ 错误: 主程序文件不存在\n\ncrypto_monitor_standalone.py 文件缺失" buttons {"确定"} default button "确定" with icon stop with title "加密货币套利监控"'
        exit 1
    fi

    # 显示启动消息
    osascript -e 'display dialog "🚀 启动加密货币套利监控系统\n\n• 支持 10 种主流加密货币\n• 监控 9 个主要交易所\n• 实时套利机会分析\n\n程序将在终端中运行..." buttons {"启动"} default button "启动" with icon note with title "加密货币套利监控" giving up after 5'

    # 在终端中运行主程序
    osascript << APPLESCRIPT
tell application "Terminal"
    activate
    set newTab to do script "cd '$RESOURCES_PATH' && echo '🎯 加密货币套利监控系统 v2.0' && echo '应用程序路径: $APP_PATH' && echo '资源路径: $RESOURCES_PATH' && echo '' && $PYTHON_CMD crypto_monitor_standalone.py"
    set custom title of newTab to "加密货币套利监控"
end tell
APPLESCRIPT

    log_message "主程序启动完成"
}

# 主执行流程
main() {
    check_python
    install_dependencies
    start_main_program
}

# 执行主函数
main

