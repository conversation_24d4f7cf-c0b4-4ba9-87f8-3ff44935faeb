#!/usr/bin/env python3
"""
创建简单的应用程序图标
"""
try:
    from PIL import Image, ImageDraw, ImageFont
    import os
    
    # 创建 512x512 的图标
    size = 512
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # 绘制背景圆形
    margin = 20
    draw.ellipse([margin, margin, size-margin, size-margin], 
                fill=(34, 139, 34, 255), outline=(0, 100, 0, 255), width=8)
    
    # 绘制货币符号
    try:
        font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 200)
    except:
        font = ImageFont.load_default()
    
    text = "₿"
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    
    x = (size - text_width) // 2
    y = (size - text_height) // 2 - 20
    
    draw.text((x, y), text, fill=(255, 255, 255, 255), font=font)
    
    # 保存为 PNG
    img.save('app_icon.png')
    print("✅ 图标创建成功")
    
except ImportError:
    print("⚠️  PIL 未安装，跳过图标创建")
except Exception as e:
    print(f"⚠️  图标创建失败: {e}")
