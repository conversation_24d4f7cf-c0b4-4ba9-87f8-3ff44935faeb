🎯 加密货币套利监控 v2.0 使用说明

📱 应用程序功能:
• 监控 10 种主流加密货币价格
• 支持 9 个主要交易所
• 实时套利机会分析
• 自动排行榜显示

🚀 使用方法:
1. 双击应用程序图标启动
2. 程序会在终端中运行
3. 选择要监控的加密货币 (1-11)
4. 选择 11 可扫描所有货币找最佳套利机会

💡 支持的加密货币:
• BTC (比特币)
• ETH (以太坊)
• BNB (币安币)
• XRP (瑞波币)
• ADA (卡尔达诺)
• DOGE (狗狗币)
• SOL (索拉纳)
• TRX (波场)
• DOT (波卡)
• AVAX (雪崩)

🏢 支持的交易所:
• Binance (币安)
• OKX
• Bybit
• MEXC
• KuCoin
• Gemini
• Gate.io
• Coinbase
• Kraken

📝 注意事项:
• 首次运行可能需要在系统偏好设置中允许运行
• 需要稳定的网络连接
• 套利机会实时变化，请及时关注

🔧 技术支持:
如遇问题，请检查:
1. Python 是否正确安装 (python3 --version)
2. 网络连接是否正常
3. 查看应用程序日志文件 (app.log)

版本: 2.0.0
更新日期: $(date '+%Y-%m-%d')
