#!/usr/bin/env python3
"""
加密货币套利监控 - macOS 独立版本
可以直接双击运行的简化版本
"""

import sys
import os
import subprocess
import json
import time
import threading
from datetime import datetime

# 检查并安装依赖
def check_and_install_dependencies():
    """检查并安装必要的依赖"""
    required_packages = ['requests', 'websocket-client', 'pytz']
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            print(f"🔧 安装依赖: {package}")
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", package])
                print(f"✅ {package} 安装成功")
            except subprocess.CalledProcessError:
                print(f"❌ {package} 安装失败，请手动安装")
                return False
    return True

# 安装依赖
if not check_and_install_dependencies():
    input("按回车键退出...")
    sys.exit(1)

# 导入依赖
import requests
import pytz
from collections import defaultdict

class SimpleCryptoMonitor:
    """简化的加密货币监控器"""
    
    def __init__(self):
        self.running = False
        self.prices = {}
        self.local_tz = pytz.timezone('Asia/Shanghai')
        
        # 支持的加密货币
        self.cryptocurrencies = {
            '1': {'symbol': 'BTC', 'name': '比特币 (Bitcoin)'},
            '2': {'symbol': 'ETH', 'name': '以太坊 (Ethereum)'},
            '3': {'symbol': 'BNB', 'name': '币安币 (Binance Coin)'},
            '4': {'symbol': 'XRP', 'name': '瑞波币 (Ripple)'},
            '5': {'symbol': 'ADA', 'name': '卡尔达诺 (Cardano)'},
            '6': {'symbol': 'DOGE', 'name': '狗狗币 (Dogecoin)'},
            '7': {'symbol': 'SOL', 'name': '索拉纳 (Solana)'},
            '8': {'symbol': 'TRX', 'name': '波场 (Tron)'},
            '9': {'symbol': 'DOT', 'name': '波卡 (Polkadot)'},
            '10': {'symbol': 'AVAX', 'name': '雪崩 (Avalanche)'},
            '11': {'symbol': 'ALL', 'name': '扫描最大的套利货币'}
        }
        
        # 交易所配置
        self.exchanges = {
            'Binance': 'https://api.binance.com/api/v3/ticker/price?symbol={}',
            'OKX': 'https://www.okx.com/api/v5/market/ticker?instId={}',
            'Bybit': 'https://api.bybit.com/v5/market/tickers?category=spot&symbol={}',
            'MEXC': 'https://api.mexc.com/api/v3/ticker/price?symbol={}',
            'KuCoin': 'https://api.kucoin.com/api/v1/market/orderbook/level1?symbol={}',
            'Gemini': 'https://api.gemini.com/v1/pubticker/{}',
            'Gate.io': 'https://api.gateio.ws/api/v4/spot/tickers?currency_pair={}',
            'Coinbase': 'https://api.exchange.coinbase.com/products/{}/ticker',
            'Kraken': 'https://api.kraken.com/0/public/Ticker?pair={}'
        }

    def get_symbol_format(self, exchange, crypto_symbol):
        """获取交易所特定的交易对格式"""
        formats = {
            'Binance': f'{crypto_symbol}USDT',
            'OKX': f'{crypto_symbol}-USDT',
            'Bybit': f'{crypto_symbol}USDT',
            'MEXC': f'{crypto_symbol}USDT',
            'KuCoin': f'{crypto_symbol}-USDT',
            'Gemini': f'{crypto_symbol}USD',
            'Gate.io': f'{crypto_symbol}_USDT',
            'Coinbase': f'{crypto_symbol}-USD',
            'Kraken': f'{crypto_symbol}USD'
        }
        return formats.get(exchange, f'{crypto_symbol}USDT')

    def get_price(self, exchange, symbol_format, crypto_symbol):
        """获取单个交易所价格"""
        try:
            headers = {"User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36"}
            url = self.exchanges[exchange].format(symbol_format)
            
            print(f"   📡 获取 {exchange} 价格...")
            response = requests.get(url, headers=headers, timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                price = self.extract_price(exchange, data)
                if price:
                    print(f"   ✅ {exchange}: ${price:.4f}")
                    return price
                else:
                    print(f"   ❌ {exchange}: 价格解析失败")
            else:
                print(f"   ❌ {exchange}: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ {exchange}: {str(e)}")
        
        return None

    def extract_price(self, exchange, data):
        """从API响应中提取价格"""
        try:
            if exchange == 'Binance':
                return float(data['price'])
            elif exchange == 'OKX':
                return float(data['data'][0]['last'])
            elif exchange == 'Bybit':
                return float(data['result']['list'][0]['lastPrice'])
            elif exchange == 'MEXC':
                return float(data['price'])
            elif exchange == 'KuCoin':
                return float(data['data']['price'])
            elif exchange == 'Gemini':
                return float(data['last'])
            elif exchange == 'Gate.io':
                return float(data[0]['last'])
            elif exchange == 'Coinbase':
                return float(data['price'])
            elif exchange == 'Kraken':
                pair_key = list(data['result'].keys())[0]
                return float(data['result'][pair_key]['c'][0])
        except:
            return None

    def scan_crypto(self, crypto_symbol):
        """扫描单个加密货币的价格"""
        print(f"\n🔍 正在测试 {self.get_crypto_name(crypto_symbol)} ({crypto_symbol})...")
        
        prices = {}
        
        for exchange in self.exchanges.keys():
            # 跳过不支持的交易对
            if exchange == 'Gemini' and crypto_symbol in ['BNB', 'ADA', 'TRX']:
                print(f"   ❌ {exchange}: 不支持 {crypto_symbol}")
                continue
                
            symbol_format = self.get_symbol_format(exchange, crypto_symbol)
            price = self.get_price(exchange, symbol_format, crypto_symbol)
            
            if price:
                prices[exchange] = price
        
        if len(prices) >= 2:
            self.analyze_arbitrage(crypto_symbol, prices)
            return prices
        else:
            print(f"   ⚠️ 获取到的价格数据不足 ({len(prices)} 个交易所)")
            return {}

    def get_crypto_name(self, symbol):
        """获取加密货币中文名称"""
        names = {
            'BTC': '比特币', 'ETH': '以太坊', 'BNB': '币安币', 'XRP': '瑞波币',
            'ADA': '卡尔达诺', 'DOGE': '狗狗币', 'SOL': '索拉纳', 'TRX': '波场',
            'DOT': '波卡', 'AVAX': '雪崩'
        }
        return names.get(symbol, symbol)

    def analyze_arbitrage(self, crypto_symbol, prices):
        """分析套利机会"""
        if len(prices) < 2:
            return
        
        max_price = max(prices.values())
        min_price = min(prices.values())
        max_exchange = [k for k, v in prices.items() if v == max_price][0]
        min_exchange = [k for k, v in prices.items() if v == min_price][0]
        
        price_diff = max_price - min_price
        arbitrage_percent = (price_diff / min_price) * 100
        
        print(f"\n📊 {self.get_crypto_name(crypto_symbol)} 价差分析:")
        print(f"   💰 套利空间: {arbitrage_percent:.3f}%")
        print(f"   📈 最高价: {max_exchange} ${max_price:.4f}")
        print(f"   📉 最低价: {min_exchange} ${min_price:.4f}")
        print(f"   💵 价差: ${price_diff:.4f}")
        print(f"   🏢 获取到 {len(prices)} 个交易所数据")
        
        return {
            'symbol': crypto_symbol,
            'arbitrage_percent': arbitrage_percent,
            'max_exchange': max_exchange,
            'max_price': max_price,
            'min_exchange': min_exchange,
            'min_price': min_price,
            'price_diff': price_diff,
            'exchange_count': len(prices)
        }

    def scan_all_cryptos(self):
        """扫描所有加密货币找最佳套利机会"""
        print("\n🚀 开始扫描所有加密货币...")
        
        arbitrage_opportunities = []
        
        for i in range(1, 11):  # 1-10
            crypto_info = self.cryptocurrencies[str(i)]
            crypto_symbol = crypto_info['symbol']
            
            prices = self.scan_crypto(crypto_symbol)
            if prices:
                arbitrage = self.analyze_arbitrage(crypto_symbol, prices)
                if arbitrage:
                    arbitrage_opportunities.append(arbitrage)
        
        # 排序并显示结果
        if arbitrage_opportunities:
            arbitrage_opportunities.sort(key=lambda x: x['arbitrage_percent'], reverse=True)
            self.display_arbitrage_ranking(arbitrage_opportunities)

    def display_arbitrage_ranking(self, opportunities):
        """显示套利机会排行榜"""
        print("\n" + "=" * 80)
        print("🏆 套利机会排行榜")
        print("=" * 80)
        
        for i, opp in enumerate(opportunities[:5], 1):
            print(f"{i}. {self.get_crypto_name(opp['symbol'])} ({opp['symbol']})")
            print(f"   💎 套利空间: {opp['arbitrage_percent']:.3f}%")
            print(f"   🔥 买入: {opp['min_exchange']} ${opp['min_price']:.4f}")
            print(f"   💰 卖出: {opp['max_exchange']} ${opp['max_price']:.4f}")
            print(f"   💵 利润: ${opp['price_diff']:.4f}")
            print()
        
        # 显示最佳机会
        best = opportunities[0]
        print("🎯 " + "=" * 76)
        print("🚀 最佳套利机会: {} ({})".format(self.get_crypto_name(best['symbol']), best['symbol']))
        print(f"💎 套利空间: {best['arbitrage_percent']:.3f}%")
        print("最高价格        | 最低价格          | 相差")
        print(f"{best['max_exchange']}: ${best['max_price']:.4f}    | {best['min_exchange']}: ${best['min_price']:.4f} | ${best['price_diff']:.4f}")
        print("🎯 " + "=" * 76)

    def show_menu(self):
        """显示菜单"""
        print("\n🚀 多交易所实时价格监控系统")
        print("=" * 50)
        print("支持的主流加密货币:")
        
        for key, crypto in self.cryptocurrencies.items():
            print(f"{key}. {crypto['name']}")
        
        print("=" * 50)

    def run(self):
        """运行监控器"""
        print("🎯 加密货币套利监控 - macOS 版")
        print("=" * 50)
        
        while True:
            self.show_menu()
            
            try:
                choice = input("\n请选择要测试的加密货币 (1-11) 或输入 'q' 退出: ").strip()
                
                if choice.lower() == 'q':
                    print("👋 再见！")
                    break
                
                if choice in self.cryptocurrencies:
                    crypto_info = self.cryptocurrencies[choice]
                    
                    if choice == '11':  # 扫描所有
                        self.scan_all_cryptos()
                    else:
                        self.scan_crypto(crypto_info['symbol'])
                else:
                    print("❌ 无效选择，请输入 1-11 或 'q'")
                    
            except KeyboardInterrupt:
                print("\n👋 再见！")
                break
            except Exception as e:
                print(f"❌ 发生错误: {e}")

def main():
    """主函数"""
    try:
        monitor = SimpleCryptoMonitor()
        monitor.run()
    except KeyboardInterrupt:
        print("\n👋 程序已退出")
    except Exception as e:
        print(f"❌ 程序错误: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
