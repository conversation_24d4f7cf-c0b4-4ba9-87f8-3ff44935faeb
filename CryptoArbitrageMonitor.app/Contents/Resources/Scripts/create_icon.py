#!/usr/bin/env python3
"""
创建专业的应用程序图标
支持多种尺寸和格式
"""
import os
import sys

def create_simple_icon():
    """创建简单的文本图标"""
    try:
        # 创建 icns 文件的替代方案 - 使用 sips 命令
        icon_text = """<?xml version="1.0" encoding="UTF-8"?>
<svg width="512" height="512" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2E7D32;stop-opacity:1" />
    </linearGradient>
  </defs>
  <circle cx="256" cy="256" r="240" fill="url(#grad1)" stroke="#1B5E20" stroke-width="8"/>
  <text x="256" y="320" font-family="Arial, sans-serif" font-size="200" font-weight="bold"
        text-anchor="middle" fill="white">₿</text>
  <text x="256" y="400" font-family="Arial, sans-serif" font-size="40"
        text-anchor="middle" fill="white">ARBITRAGE</text>
</svg>"""

        with open('../app_icon.svg', 'w') as f:
            f.write(icon_text)

        print("✅ SVG 图标创建成功")
        return True

    except Exception as e:
        print(f"⚠️  图标创建失败: {e}")
        return False

def create_png_icon():
    """使用 PIL 创建 PNG 图标"""
    try:
        from PIL import Image, ImageDraw, ImageFont

        # 创建多个尺寸的图标
        sizes = [16, 32, 64, 128, 256, 512]

        for size in sizes:
            img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
            draw = ImageDraw.Draw(img)

            # 绘制背景圆形
            margin = max(2, size // 20)
            draw.ellipse([margin, margin, size-margin, size-margin],
                        fill=(76, 175, 80, 255), outline=(27, 94, 32, 255), width=max(1, size//64))

            # 绘制货币符号
            font_size = max(size//3, 12)
            try:
                font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", font_size)
            except:
                font = ImageFont.load_default()

            text = "₿"
            bbox = draw.textbbox((0, 0), text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]

            x = (size - text_width) // 2
            y = (size - text_height) // 2 - size//20

            draw.text((x, y), text, fill=(255, 255, 255, 255), font=font)

            # 保存图标
            img.save(f'../app_icon_{size}x{size}.png')

        print("✅ PNG 图标创建成功")
        return True

    except ImportError:
        print("⚠️  PIL 未安装，使用简单图标")
        return False
    except Exception as e:
        print(f"⚠️  PNG 图标创建失败: {e}")
        return False

if __name__ == "__main__":
    success = create_png_icon()
    if not success:
        create_simple_icon()
