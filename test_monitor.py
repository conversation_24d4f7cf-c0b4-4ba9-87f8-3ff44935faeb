#!/usr/bin/env python3
"""
简化版多交易所价格监控测试
"""

import requests
import json
import time
from datetime import datetime

def test_basic_functionality():
    """测试基本功能"""
    print("🚀 多交易所实时价格监控系统")
    print("="*50)
    print("支持的主流加密货币:")
    print("1. BTC  - 比特币 (Bitcoin)")
    print("2. ETH  - 以太坊 (Ethereum)")
    print("3. BNB  - 币安币 (Binance Coin)")
    print("4. XRP  - 瑞波币 (Ripple)")
    print("5. ADA  - 卡尔达诺 (Cardano)")
    print("6. DOGE - 狗狗币 (Dogecoin)")
    print("7. SOL  - 索拉纳 (Solana)")
    print("8. TRX  - 波场 (Tron)")
    print("9. DOT  - 波卡 (Polkadot)")
    print("10. AVAX - 雪崩 (Avalanche)")
    print("11. 扫描最大的套利货币")
    print("="*50)

def get_quick_price(exchange, symbol_format, crypto_symbol):
    """快速获取单个交易所价格"""
    try:
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }

        if exchange == 'Binance':
            url = f"https://api.binance.com/api/v3/ticker/price?symbol={symbol_format}"
            response = requests.get(url, headers=headers, timeout=3)
            if response.status_code == 200:
                data = response.json()
                return float(data['price'])

        elif exchange == 'OKX':
            url = f"https://www.okx.com/api/v5/market/ticker?instId={symbol_format}"
            response = requests.get(url, headers=headers, timeout=3)
            if response.status_code == 200:
                data = response.json()
                if data.get('data'):
                    return float(data['data'][0]['last'])

        elif exchange == 'Bybit':
            url = f"https://api.bybit.com/v5/market/tickers?category=spot&symbol={symbol_format}"
            response = requests.get(url, headers=headers, timeout=3)
            if response.status_code == 200:
                data = response.json()
                if data.get('result', {}).get('list'):
                    return float(data['result']['list'][0]['lastPrice'])

        elif exchange == 'MEXC':
            url = f"https://api.mexc.com/api/v3/ticker/price?symbol={symbol_format}"
            response = requests.get(url, headers=headers, timeout=3)
            if response.status_code == 200:
                data = response.json()
                return float(data['price'])

        elif exchange == 'KuCoin':
            url = f"https://api.kucoin.com/api/v1/market/orderbook/level1?symbol={symbol_format}"
            response = requests.get(url, headers=headers, timeout=3)
            if response.status_code == 200:
                data = response.json()
                if data.get('code') == '200000' and 'data' in data:
                    return float(data['data']['price'])

        elif exchange == 'Gate.io':
            url = f"https://api.gateio.ws/api/v4/spot/tickers?currency_pair={symbol_format}"
            response = requests.get(url, headers=headers, timeout=3)
            if response.status_code == 200:
                data = response.json()
                if data:
                    return float(data[0]['last'])

        elif exchange == 'Coinbase':
            url = f"https://api.coinbase.com/v2/exchange-rates?currency={crypto_symbol}"
            response = requests.get(url, headers=headers, timeout=3)
            if response.status_code == 200:
                data = response.json()
                if 'data' in data and 'rates' in data['data']:
                    usd_rate = data['data']['rates'].get('USD')
                    if usd_rate:
                        return float(usd_rate)

        elif exchange == 'Kraken':
            kraken_symbol = symbol_format.replace('/', '').replace('-', '')
            if kraken_symbol == 'BTCUSD':
                kraken_symbol = 'XXBTZUSD'
            elif kraken_symbol == 'ETHUSD':
                kraken_symbol = 'XETHZUSD'

            url = f"https://api.kraken.com/0/public/Ticker?pair={kraken_symbol}"
            response = requests.get(url, headers=headers, timeout=3)
            if response.status_code == 200:
                data = response.json()
                if 'result' in data and data['result']:
                    pair_data = list(data['result'].values())[0]
                    if 'c' in pair_data:
                        return float(pair_data['c'][0])

        elif exchange == 'Gemini':
            url = f"https://api.gemini.com/v1/pubticker/{symbol_format.lower()}"
            response = requests.get(url, headers=headers, timeout=3)
            if response.status_code == 200:
                data = response.json()
                return float(data['last'])

    except Exception as e:
        print(f"❌ {exchange} 获取 {crypto_symbol} 价格失败: {str(e)}")
        return None

def test_single_crypto(crypto_symbol, crypto_name):
    """测试单个加密货币的价格获取"""
    print(f"\n🔍 正在测试 {crypto_name} ({crypto_symbol})...")
    
    # 交易所配置
    exchanges = {
        'Binance': f'{crypto_symbol}USDT',
        'OKX': f'{crypto_symbol}-USDT',
        'Bybit': f'{crypto_symbol}USDT',
        'MEXC': f'{crypto_symbol}USDT',
        'KuCoin': f'{crypto_symbol}-USDT',
        'Gemini': f'{crypto_symbol}USD',
        'Gate.io': f'{crypto_symbol}_USDT',
        'Coinbase': f'{crypto_symbol}-USD',
        'Kraken': f'{crypto_symbol}/USD'
    }
    
    prices = {}
    
    for exchange, symbol_format in exchanges.items():
        print(f"   📡 获取 {exchange} 价格...")
        price = get_quick_price(exchange, symbol_format, crypto_symbol)
        if price:
            prices[exchange] = {
                'price': price,
                'timestamp': datetime.now(),
                'symbol': symbol_format
            }
            print(f"   ✅ {exchange}: ${price:.4f}")
        else:
            print(f"   ❌ {exchange}: 获取失败")
    
    if len(prices) >= 2:
        # 计算价差
        price_values = [data['price'] for data in prices.values()]
        max_price = max(price_values)
        min_price = min(price_values)
        price_diff = max_price - min_price
        percentage_diff = (price_diff / min_price) * 100
        
        # 找出最高价和最低价的交易所
        max_exchange = next(ex for ex, data in prices.items() if data['price'] == max_price)
        min_exchange = next(ex for ex, data in prices.items() if data['price'] == min_price)
        
        print(f"\n📊 {crypto_name} 价差分析:")
        print(f"   💰 套利空间: {percentage_diff:.3f}%")
        print(f"   📈 最高价: {max_exchange} ${max_price:.4f}")
        print(f"   📉 最低价: {min_exchange} ${min_price:.4f}")
        print(f"   💵 价差: ${price_diff:.4f}")
        print(f"   🏢 获取到 {len(prices)} 个交易所数据")
        
        return {
            'crypto': crypto_symbol,
            'name': crypto_name,
            'arbitrage': {
                'percentage_diff': percentage_diff,
                'max_exchange': max_exchange,
                'max_price': max_price,
                'min_exchange': min_exchange,
                'min_price': min_price,
                'price_diff': price_diff,
                'exchange_count': len(prices)
            },
            'prices': prices
        }
    else:
        print(f"   ❌ 数据不足 (仅获取到 {len(prices)} 个交易所数据)")
        return None

def main():
    """主函数"""
    test_basic_functionality()
    
    # 用户选择
    while True:
        try:
            choice = input("\n请选择要测试的加密货币 (1-11) 或输入 'q' 退出: ").strip()
            
            if choice.lower() == 'q':
                print("👋 再见！")
                return
            
            crypto_configs = {
                "1": {"symbol": "BTC", "name": "比特币"},
                "2": {"symbol": "ETH", "name": "以太坊"},
                "3": {"symbol": "BNB", "name": "币安币"},
                "4": {"symbol": "XRP", "name": "瑞波币"},
                "5": {"symbol": "ADA", "name": "卡尔达诺"},
                "6": {"symbol": "DOGE", "name": "狗狗币"},
                "7": {"symbol": "SOL", "name": "索拉纳"},
                "8": {"symbol": "TRX", "name": "波场"},
                "9": {"symbol": "DOT", "name": "波卡"},
                "10": {"symbol": "AVAX", "name": "雪崩"}
            }
            
            if choice == "11":
                print("\n🚀 开始扫描所有加密货币...")
                opportunities = []
                
                for config in crypto_configs.values():
                    result = test_single_crypto(config['symbol'], config['name'])
                    if result:
                        opportunities.append(result)
                    time.sleep(1)  # 避免API限制
                
                if opportunities:
                    # 按套利百分比排序
                    opportunities.sort(key=lambda x: x['arbitrage']['percentage_diff'], reverse=True)
                    
                    print("\n" + "="*80)
                    print("🏆 套利机会排行榜")
                    print("="*80)
                    
                    for i, opp in enumerate(opportunities[:5], 1):
                        arb = opp['arbitrage']
                        print(f"{i}. {opp['name']} ({opp['crypto']})")
                        print(f"   💎 套利空间: {arb['percentage_diff']:.3f}%")
                        print(f"   🔥 买入: {arb['min_exchange']} ${arb['min_price']:.4f}")
                        print(f"   💰 卖出: {arb['max_exchange']} ${arb['max_price']:.4f}")
                        print(f"   💵 利润: ${arb['price_diff']:.4f}")
                        print()
                    
                    if opportunities:
                        best = opportunities[0]
                        best_arb = best['arbitrage']
                        print("🎯 " + "="*76)
                        print(f"🚀 最佳套利机会: {best['name']} ({best['crypto']})")
                        print(f"💎 套利空间: {best_arb['percentage_diff']:.3f}%")
                        print("最高价格        | 最低价格          | 相差")
                        print(f"{best_arb['max_exchange']}: ${best_arb['max_price']:.4f}    | {best_arb['min_exchange']}: ${best_arb['min_price']:.4f} | ${best_arb['price_diff']:.4f}")
                        print("🎯 " + "="*76)
                
            elif choice in crypto_configs:
                config = crypto_configs[choice]
                test_single_crypto(config['symbol'], config['name'])
            else:
                print("❌ 无效选择，请输入 1-11 之间的数字")
                
        except KeyboardInterrupt:
            print("\n👋 程序已退出")
            return
        except Exception as e:
            print(f"❌ 发生错误: {str(e)}")
            continue

if __name__ == "__main__":
    main()
