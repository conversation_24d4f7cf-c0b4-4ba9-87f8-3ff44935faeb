#!/usr/bin/env python3
"""
macOS 应用程序构建脚本
使用 PyInstaller 将 Python 脚本打包成 macOS 可执行文件
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def install_pyinstaller():
    """安装 PyInstaller"""
    print("🔧 安装 PyInstaller...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], check=True)
        print("✅ PyInstaller 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ PyInstaller 安装失败: {e}")
        return False

def create_spec_file():
    """创建 PyInstaller 规格文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['multi_exchange_price_monitor.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[
        'websocket',
        'websocket._core',
        'websocket._exceptions',
        'websocket._socket',
        'websocket._ssl_compat',
        'websocket._utils',
        'requests',
        'pytz',
        'ssl',
        'json',
        'threading',
        'datetime',
        'collections',
        'logging'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='CryptoArbitrageMonitor',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None
)

# 创建 macOS 应用程序包
app = BUNDLE(
    exe,
    name='CryptoArbitrageMonitor.app',
    icon=None,
    bundle_identifier='com.crypto.arbitrage.monitor',
    version='1.0.0',
    info_plist={
        'CFBundleName': 'Crypto Arbitrage Monitor',
        'CFBundleDisplayName': 'Crypto Arbitrage Monitor',
        'CFBundleVersion': '1.0.0',
        'CFBundleShortVersionString': '1.0.0',
        'NSHighResolutionCapable': True,
        'LSMinimumSystemVersion': '10.13.0',
    }
)
'''
    
    with open('crypto_monitor.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    print("✅ 创建 PyInstaller 规格文件")

def build_executable():
    """构建可执行文件"""
    print("🚀 开始构建 macOS 应用程序...")
    
    try:
        # 使用规格文件构建
        subprocess.run([
            "pyinstaller", 
            "--clean",
            "--noconfirm",
            "crypto_monitor.spec"
        ], check=True)
        
        print("✅ 构建完成！")
        print("📱 应用程序位置: ./dist/CryptoArbitrageMonitor.app")
        print("💻 可执行文件位置: ./dist/CryptoArbitrageMonitor")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败: {e}")
        return False

def create_launcher_script():
    """创建启动脚本"""
    launcher_content = '''#!/bin/bash
# Crypto Arbitrage Monitor 启动脚本

echo "🚀 启动加密货币套利监控系统..."

# 获取脚本所在目录
DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"

# 检查可执行文件是否存在
if [ -f "$DIR/dist/CryptoArbitrageMonitor" ]; then
    echo "📱 启动应用程序..."
    "$DIR/dist/CryptoArbitrageMonitor"
elif [ -d "$DIR/dist/CryptoArbitrageMonitor.app" ]; then
    echo "📱 启动 macOS 应用程序..."
    open "$DIR/dist/CryptoArbitrageMonitor.app"
else
    echo "❌ 找不到可执行文件，请先运行构建脚本"
    echo "运行: python3 build_macos_app.py"
fi
'''
    
    with open('launch_crypto_monitor.sh', 'w', encoding='utf-8') as f:
        f.write(launcher_content)
    
    # 设置执行权限
    os.chmod('launch_crypto_monitor.sh', 0o755)
    print("✅ 创建启动脚本: launch_crypto_monitor.sh")

def main():
    """主函数"""
    print("🎯 macOS 应用程序构建工具")
    print("=" * 50)
    
    # 检查是否在虚拟环境中
    if not hasattr(sys, 'real_prefix') and not (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("⚠️  建议在虚拟环境中运行此脚本")
        response = input("是否继续? (y/N): ")
        if response.lower() != 'y':
            return
    
    # 安装 PyInstaller
    if not install_pyinstaller():
        return
    
    # 创建规格文件
    create_spec_file()
    
    # 构建可执行文件
    if build_executable():
        create_launcher_script()
        
        print("\n🎉 构建完成！")
        print("=" * 50)
        print("📁 输出文件:")
        print("  • dist/CryptoArbitrageMonitor.app - macOS 应用程序包")
        print("  • dist/CryptoArbitrageMonitor - 命令行可执行文件")
        print("  • launch_crypto_monitor.sh - 启动脚本")
        print("\n🚀 使用方法:")
        print("  1. 双击 CryptoArbitrageMonitor.app 启动应用")
        print("  2. 或运行: ./launch_crypto_monitor.sh")
        print("  3. 或直接运行: ./dist/CryptoArbitrageMonitor")
        
    else:
        print("❌ 构建失败，请检查错误信息")

if __name__ == "__main__":
    main()
