#!/usr/bin/env python3
"""
多交易所实时价格监控系统
整合多个交易所的价格数据并显示价差分析
"""

import requests
import websocket
import json
import time
import threading
from datetime import datetime
import pytz
import logging
from collections import defaultdict
import ssl

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class MultiExchangePriceMonitor:
    def __init__(self, symbol="BTC", base_currency="USDT"):
        self.symbol = symbol
        self.base_currency = base_currency
        self.local_tz = pytz.timezone('Asia/Shanghai')
        self.running = False
        
        # 存储各交易所的最新价格
        self.prices = {}
        self.price_lock = threading.Lock()
        
        # 交易所配置
        self.exchanges = {
            'Gemini': {'type': 'rest', 'symbol_format': f'{symbol}USD'},
            'Gate.io': {'type': 'websocket', 'symbol_format': f'{symbol}_{base_currency}'},
            'MEXC': {'type': 'rest', 'symbol_format': f'{symbol}{base_currency}'},
            'Bitfinex': {'type': 'websocket', 'symbol_format': f'{symbol}USD'},
            'Bitstamp': {'type': 'websocket', 'symbol_format': f'{symbol.lower()}usd'},
            'Binance': {'type': 'websocket', 'symbol_format': f'{symbol}{base_currency}'},
            'Coinbase': {'type': 'rest', 'symbol_format': f'{symbol}-USD'},  # 改为REST
            'Kraken': {'type': 'rest', 'symbol_format': f'{symbol}/USD'},    # 改为REST
            'KuCoin': {'type': 'rest', 'symbol_format': f'{symbol}-{base_currency}'},  # 改为REST
            'Bybit': {'type': 'websocket', 'symbol_format': f'{symbol}{base_currency}'},
            'OKX': {'type': 'websocket', 'symbol_format': f'{symbol}-{base_currency}'}
        }
        
        # WebSocket连接存储
        self.ws_connections = {}
        
    def start(self):
        """启动所有交易所的价格监控"""
        self.running = True
        logging.info("启动多交易所价格监控系统")
        
        # 启动各交易所的价格获取线程
        threads = []
        
        for exchange, config in self.exchanges.items():
            if config['type'] == 'rest':
                thread = threading.Thread(target=self.start_rest_feed, args=(exchange, config))
            else:
                thread = threading.Thread(target=self.start_websocket_feed, args=(exchange, config))
            
            thread.daemon = True
            threads.append(thread)
            thread.start()
        
        # 启动价格显示线程
        display_thread = threading.Thread(target=self.display_prices)
        display_thread.daemon = True
        display_thread.start()
        
        return threads
    
    def start_rest_feed(self, exchange, config):
        """启动REST API价格获取"""
        symbol = config['symbol_format']

        # 根据不同交易所设置URL和处理逻辑
        if exchange == 'Gemini':
            url = f"https://api.gemini.com/v1/pubticker/{symbol.lower()}"
        elif exchange == 'MEXC':
            url = f"https://api.mexc.com/api/v3/ticker/price?symbol={symbol}"
        elif exchange == 'Coinbase':
            # 直接调用之前定义的方法
            self.start_coinbase_ws(symbol)
            return
        elif exchange == 'Kraken':
            # 直接调用之前定义的方法
            self.start_kraken_ws(symbol)
            return
        elif exchange == 'KuCoin':
            # 直接调用之前定义的方法
            self.start_kucoin_ws(symbol)
            return

        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }

        while self.running:
            try:
                response = requests.get(url, headers=headers, timeout=5)
                if response.status_code == 200:
                    data = response.json()

                    if exchange == 'Gemini':
                        price = float(data['last'])
                    elif exchange == 'MEXC':
                        price = float(data['price'])

                    with self.price_lock:
                        self.prices[exchange] = {
                            'price': price,
                            'timestamp': datetime.now(self.local_tz),
                            'symbol': symbol
                        }

            except Exception as e:
                logging.error(f"{exchange} REST API错误: {str(e)}")

            time.sleep(1)  # 1秒轮询一次
    
    def start_websocket_feed(self, exchange, config):
        """启动WebSocket价格获取"""
        symbol = config['symbol_format']

        if exchange == 'Gate.io':
            self.start_gateio_ws(symbol)
        elif exchange == 'Bitfinex':
            self.start_bitfinex_ws(symbol)
        elif exchange == 'Bitstamp':
            self.start_bitstamp_ws(symbol)
        elif exchange == 'Binance':
            self.start_binance_ws(symbol)
        elif exchange == 'Coinbase':
            self.start_coinbase_ws(symbol)
        elif exchange == 'Kraken':
            self.start_kraken_ws(symbol)
        elif exchange == 'KuCoin':
            self.start_kucoin_ws(symbol)
        elif exchange == 'Bybit':
            self.start_bybit_ws(symbol)
        elif exchange == 'OKX':
            self.start_okx_ws(symbol)
    
    def start_gateio_ws(self, symbol):
        """Gate.io WebSocket连接"""
        def on_message(ws, message):
            try:
                data = json.loads(message)
                if data.get("method") == "ticker.update":
                    params = data.get("params", [])
                    if len(params) >= 2:
                        ticker = params[1]
                        price = float(ticker.get("last", 0))
                        
                        with self.price_lock:
                            self.prices['Gate.io'] = {
                                'price': price,
                                'timestamp': datetime.now(self.local_tz),
                                'symbol': symbol
                            }
            except Exception as e:
                logging.error(f"Gate.io数据处理错误: {str(e)}")
        
        def on_open(ws):
            subscribe_msg = {
                "id": int(time.time()),
                "method": "ticker.subscribe",
                "params": [symbol]
            }
            ws.send(json.dumps(subscribe_msg))
        
        def on_error(ws, error):
            logging.error(f"Gate.io WebSocket错误: {str(error)}")
        
        def on_close(ws, close_status_code, close_msg):
            if self.running:
                time.sleep(5)
                self.start_gateio_ws(symbol)
        
        ws = websocket.WebSocketApp(
            "wss://ws.gate.io/v4/",
            on_open=on_open,
            on_message=on_message,
            on_error=on_error,
            on_close=on_close
        )
        
        self.ws_connections['Gate.io'] = ws
        ws.run_forever()
    
    def start_bitfinex_ws(self, symbol):
        """Bitfinex WebSocket连接"""
        channel_id = None
        
        def on_message(ws, message):
            nonlocal channel_id
            try:
                data = json.loads(message)
                
                if isinstance(data, dict) and data.get("event") == "subscribed":
                    channel_id = data.get("chanId")
                    return
                
                if isinstance(data, list) and len(data) > 1 and data[0] == channel_id:
                    ticker_data = data[1]
                    if isinstance(ticker_data, list) and len(ticker_data) >= 7:
                        price = float(ticker_data[6])
                        
                        with self.price_lock:
                            self.prices['Bitfinex'] = {
                                'price': price,
                                'timestamp': datetime.now(self.local_tz),
                                'symbol': symbol
                            }
            except Exception as e:
                logging.error(f"Bitfinex数据处理错误: {str(e)}")
        
        def on_open(ws):
            subscribe_msg = {
                "event": "subscribe",
                "channel": "ticker",
                "symbol": f"t{symbol}"
            }
            ws.send(json.dumps(subscribe_msg))
        
        def on_error(ws, error):
            logging.error(f"Bitfinex WebSocket错误: {str(error)}")
        
        def on_close(ws, close_status_code, close_msg):
            if self.running:
                time.sleep(5)
                self.start_bitfinex_ws(symbol)
        
        ws = websocket.WebSocketApp(
            "wss://api-pub.bitfinex.com/ws/2",
            on_open=on_open,
            on_message=on_message,
            on_error=on_error,
            on_close=on_close
        )
        
        self.ws_connections['Bitfinex'] = ws
        ws.run_forever()
    
    def start_bitstamp_ws(self, symbol):
        """Bitstamp WebSocket连接"""
        def on_message(ws, message):
            try:
                data = json.loads(message)
                if data.get("event") == "trade":
                    trade = data["data"]
                    price = float(trade['price'])
                    
                    with self.price_lock:
                        self.prices['Bitstamp'] = {
                            'price': price,
                            'timestamp': datetime.now(self.local_tz),
                            'symbol': symbol
                        }
            except Exception as e:
                logging.error(f"Bitstamp数据处理错误: {str(e)}")
        
        def on_open(ws):
            subscribe_msg = {
                "event": "bts:subscribe",
                "data": {
                    "channel": f"live_trades_{symbol}"
                }
            }
            ws.send(json.dumps(subscribe_msg))
        
        def on_error(ws, error):
            logging.error(f"Bitstamp WebSocket错误: {str(error)}")
        
        def on_close(ws, close_status_code, close_msg):
            if self.running:
                time.sleep(5)
                self.start_bitstamp_ws(symbol)
        
        ws = websocket.WebSocketApp(
            "wss://ws.bitstamp.net",
            on_open=on_open,
            on_message=on_message,
            on_error=on_error,
            on_close=on_close
        )
        
        self.ws_connections['Bitstamp'] = ws
        ws.run_forever()

    def start_binance_ws(self, symbol):
        """Binance WebSocket连接"""
        def on_message(ws, message):
            try:
                data = json.loads(message)
                if 'c' in data:  # 24hr ticker统计
                    price = float(data['c'])  # 最新价格

                    with self.price_lock:
                        self.prices['Binance'] = {
                            'price': price,
                            'timestamp': datetime.now(self.local_tz),
                            'symbol': symbol
                        }
            except Exception as e:
                logging.error(f"Binance数据处理错误: {str(e)}")

        def on_open(ws):
            # 订阅24hr ticker stream
            pass  # Binance的ticker stream不需要订阅消息

        def on_error(ws, error):
            logging.error(f"Binance WebSocket错误: {str(error)}")

        def on_close(ws, close_status_code, close_msg):
            if self.running:
                time.sleep(5)
                self.start_binance_ws(symbol)

        # Binance WebSocket URL格式
        ws_url = f"wss://stream.binance.com:9443/ws/{symbol.lower()}@ticker"

        ws = websocket.WebSocketApp(
            ws_url,
            on_open=on_open,
            on_message=on_message,
            on_error=on_error,
            on_close=on_close
        )

        self.ws_connections['Binance'] = ws
        ws.run_forever()

    def start_coinbase_ws(self, symbol):
        """Coinbase WebSocket连接 - 使用REST API作为备选"""
        # 由于Coinbase WebSocket可能有地理限制，使用REST API
        def poll_coinbase():
            while self.running:
                try:
                    # 转换symbol格式 BTC-USD -> BTC-USD
                    url = f"https://api.coinbase.com/v2/exchange-rates?currency={symbol.split('-')[0]}"
                    response = requests.get(url, timeout=5)
                    if response.status_code == 200:
                        data = response.json()
                        if 'data' in data and 'rates' in data['data']:
                            usd_rate = data['data']['rates'].get('USD')
                            if usd_rate:
                                price = float(usd_rate)

                                with self.price_lock:
                                    self.prices['Coinbase'] = {
                                        'price': price,
                                        'timestamp': datetime.now(self.local_tz),
                                        'symbol': symbol
                                    }
                except Exception as e:
                    logging.error(f"Coinbase REST API错误: {str(e)}")

                time.sleep(2)  # 每2秒轮询一次

        # 启动REST API轮询线程
        thread = threading.Thread(target=poll_coinbase)
        thread.daemon = True
        thread.start()

    def start_kraken_ws(self, symbol):
        """Kraken REST API连接"""
        def poll_kraken():
            while self.running:
                try:
                    # Kraken REST API - 转换symbol格式
                    kraken_symbol = symbol.replace('/', '').replace('-', '')
                    if kraken_symbol == 'BTCUSD':
                        kraken_symbol = 'XXBTZUSD'
                    elif kraken_symbol == 'ETHUSD':
                        kraken_symbol = 'XETHZUSD'

                    url = f"https://api.kraken.com/0/public/Ticker?pair={kraken_symbol}"
                    response = requests.get(url, timeout=5)
                    if response.status_code == 200:
                        data = response.json()
                        if 'result' in data and data['result']:
                            # 获取第一个交易对的数据
                            pair_data = list(data['result'].values())[0]
                            if 'c' in pair_data:
                                price = float(pair_data['c'][0])  # 最新价格

                                with self.price_lock:
                                    self.prices['Kraken'] = {
                                        'price': price,
                                        'timestamp': datetime.now(self.local_tz),
                                        'symbol': symbol
                                    }
                except Exception as e:
                    logging.error(f"Kraken REST API错误: {str(e)}")

                time.sleep(3)  # 每3秒轮询一次

        # 启动REST API轮询线程
        thread = threading.Thread(target=poll_kraken)
        thread.daemon = True
        thread.start()

    def start_kucoin_ws(self, symbol):
        """KuCoin REST API连接"""
        def poll_kucoin():
            while self.running:
                try:
                    # KuCoin REST API
                    url = f"https://api.kucoin.com/api/v1/market/orderbook/level1?symbol={symbol}"
                    response = requests.get(url, timeout=5)
                    if response.status_code == 200:
                        data = response.json()
                        if data.get('code') == '200000' and 'data' in data:
                            price = float(data['data']['price'])

                            with self.price_lock:
                                self.prices['KuCoin'] = {
                                    'price': price,
                                    'timestamp': datetime.now(self.local_tz),
                                    'symbol': symbol
                                }
                except Exception as e:
                    logging.error(f"KuCoin REST API错误: {str(e)}")

                time.sleep(2)  # 每2秒轮询一次

        # 启动REST API轮询线程
        thread = threading.Thread(target=poll_kucoin)
        thread.daemon = True
        thread.start()

    def start_bybit_ws(self, symbol):
        """Bybit WebSocket连接"""
        def on_message(ws, message):
            try:
                data = json.loads(message)
                if 'data' in data and data.get('topic'):
                    if 'tickers' in data.get('topic', ''):
                        ticker_data = data['data']
                        if 'lastPrice' in ticker_data:
                            price = float(ticker_data['lastPrice'])

                            with self.price_lock:
                                self.prices['Bybit'] = {
                                    'price': price,
                                    'timestamp': datetime.now(self.local_tz),
                                    'symbol': symbol
                                }
            except Exception as e:
                logging.error(f"Bybit数据处理错误: {str(e)}")

        def on_open(ws):
            subscribe_msg = {
                "op": "subscribe",
                "args": [f"tickers.{symbol}"]
            }
            ws.send(json.dumps(subscribe_msg))

        def on_error(ws, error):
            logging.error(f"Bybit WebSocket错误: {str(error)}")

        def on_close(ws, close_status_code, close_msg):
            if self.running:
                time.sleep(5)
                self.start_bybit_ws(symbol)

        ws = websocket.WebSocketApp(
            "wss://stream.bybit.com/v5/public/spot",
            on_open=on_open,
            on_message=on_message,
            on_error=on_error,
            on_close=on_close
        )

        self.ws_connections['Bybit'] = ws
        ws.run_forever()

    def start_okx_ws(self, symbol):
        """OKX WebSocket连接"""
        def on_message(ws, message):
            try:
                data = json.loads(message)
                if 'data' in data:
                    for ticker in data['data']:
                        if 'last' in ticker:
                            price = float(ticker['last'])

                            with self.price_lock:
                                self.prices['OKX'] = {
                                    'price': price,
                                    'timestamp': datetime.now(self.local_tz),
                                    'symbol': symbol
                                }
            except Exception as e:
                logging.error(f"OKX数据处理错误: {str(e)}")

        def on_open(ws):
            subscribe_msg = {
                "op": "subscribe",
                "args": [
                    {
                        "channel": "tickers",
                        "instId": symbol
                    }
                ]
            }
            ws.send(json.dumps(subscribe_msg))

        def on_error(ws, error):
            logging.error(f"OKX WebSocket错误: {str(error)}")

        def on_close(ws, close_status_code, close_msg):
            if self.running:
                time.sleep(5)
                self.start_okx_ws(symbol)

        ws = websocket.WebSocketApp(
            "wss://ws.okx.com:8443/ws/v5/public",
            on_open=on_open,
            on_message=on_message,
            on_error=on_error,
            on_close=on_close
        )

        self.ws_connections['OKX'] = ws
        ws.run_forever()

    def display_prices(self):
        """显示价格和价差分析"""
        print("\n" + "="*100)
        print("多交易所实时价格监控系统")
        print("="*100)

        while self.running:
            try:
                with self.price_lock:
                    current_prices = self.prices.copy()

                if len(current_prices) >= 2:
                    # 清屏并显示表头
                    print("\033[2J\033[H")  # 清屏
                    print(f"{'='*100}")
                    print(f"实时价格监控 - {self.symbol}/{self.base_currency}")
                    print(f"更新时间: {datetime.now(self.local_tz).strftime('%Y-%m-%d %H:%M:%S')}")
                    print(f"{'='*100}")

                    # 显示各交易所价格 - 按你要求的格式
                    for exchange, data in current_prices.items():
                        time_str = data['timestamp'].strftime('%H:%M:%S')
                        print(f"| {exchange:<12} | {time_str} | {data['symbol']:<10} | ${data['price']:>10.2f} |")

                    print(f"{'-'*100}")

                    # 计算价差
                    prices_only = [data['price'] for data in current_prices.values()]
                    max_price = max(prices_only)
                    min_price = min(prices_only)
                    price_diff = max_price - min_price

                    # 找出最高价和最低价的交易所
                    max_exchange = next(ex for ex, data in current_prices.items() if data['price'] == max_price)
                    min_exchange = next(ex for ex, data in current_prices.items() if data['price'] == min_price)

                    # 按你要求的格式显示价差分析
                    print(f"最高价格        | 最低价格          | 相差")
                    print(f"{max_exchange}: ${max_price:.2f}    | {min_exchange}: ${min_price:.2f} | ${price_diff:.2f}")
                    print(f"价差百分比: {(price_diff/min_price*100):.3f}%")
                    print(f"{'='*100}")

                time.sleep(3)  # 每3秒更新一次显示

            except Exception as e:
                logging.error(f"显示错误: {str(e)}")
                time.sleep(1)
    
    def close(self):
        """关闭所有连接"""
        self.running = False
        for ws in self.ws_connections.values():
            if ws:
                ws.close()
        logging.info("多交易所价格监控系统已关闭")

def start_auto_arbitrage_scan():
    """启动自动套利扫描"""
    # 10种主流加密货币配置
    cryptocurrencies = {
        "BTC": {"name": "比特币", "base": "USDT"},
        "ETH": {"name": "以太坊", "base": "USDT"},
        "BNB": {"name": "币安币", "base": "USDT"},
        "XRP": {"name": "瑞波币", "base": "USDT"},
        "ADA": {"name": "卡尔达诺", "base": "USDT"},
        "DOGE": {"name": "狗狗币", "base": "USDT"},
        "SOL": {"name": "索拉纳", "base": "USDT"},
        "TRX": {"name": "波场", "base": "USDT"},
        "DOT": {"name": "波卡", "base": "USDT"},
        "AVAX": {"name": "雪崩", "base": "USDT"}
    }

    print("🚀 自动套利扫描器启动！")
    print("="*80)
    print("正在扫描10种主流加密货币的套利机会...")
    print("="*80)

    try:
        while True:
            round_opportunities = {}

            for crypto_symbol, crypto_info in cryptocurrencies.items():
                print(f"\n🔍 正在扫描 {crypto_info['name']} ({crypto_symbol})...")

                # 创建临时监控实例获取价格
                monitor = MultiExchangePriceMonitor(
                    symbol=crypto_symbol,
                    base_currency=crypto_info['base']
                )

                # 快速获取价格数据
                prices = {}
                exchanges = monitor.exchanges

                for exchange, config in exchanges.items():
                    try:
                        if config['type'] == 'rest':
                            # 使用REST API快速获取价格
                            price = get_quick_price(exchange, config['symbol_format'], crypto_symbol)
                            if price:
                                prices[exchange] = {
                                    'price': price,
                                    'timestamp': datetime.now(monitor.local_tz),
                                    'symbol': config['symbol_format']
                                }
                    except:
                        continue

                if len(prices) >= 3:  # 至少需要3个交易所的数据
                    arbitrage = calculate_arbitrage_opportunity(prices)
                    if arbitrage:
                        round_opportunities[crypto_symbol] = {
                            'info': crypto_info,
                            'arbitrage': arbitrage,
                            'prices': prices
                        }

                        print(f"   💰 发现套利机会: {arbitrage['percentage_diff']:.3f}%")
                        print(f"   📈 最高价: {arbitrage['max_exchange']} ${arbitrage['max_price']:.4f}")
                        print(f"   📉 最低价: {arbitrage['min_exchange']} ${arbitrage['min_price']:.4f}")
                        print(f"   💵 价差: ${arbitrage['price_diff']:.4f}")
                else:
                    print(f"   ❌ 数据不足 (仅获取到 {len(prices)} 个交易所数据)")

                time.sleep(1)  # 每种货币间隔1秒

            # 分析本轮最佳套利机会
            if round_opportunities:
                analyze_best_opportunity(round_opportunities)

            print("\n" + "="*80)
            print("⏱️  等待下一轮扫描...")
            print("="*80)
            time.sleep(10)  # 等待10秒进行下一轮扫描

    except KeyboardInterrupt:
        print("\n⏹️  自动套利扫描已停止")

def get_quick_price(exchange, symbol_format, crypto_symbol):
    """快速获取单个交易所价格"""
    try:
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }

        if exchange == 'Gemini':
            url = f"https://api.gemini.com/v1/pubticker/{symbol_format.lower()}"
            response = requests.get(url, headers=headers, timeout=3)
            if response.status_code == 200:
                data = response.json()
                return float(data['last'])

        elif exchange == 'MEXC':
            url = f"https://api.mexc.com/api/v3/ticker/price?symbol={symbol_format}"
            response = requests.get(url, headers=headers, timeout=3)
            if response.status_code == 200:
                data = response.json()
                return float(data['price'])

        elif exchange == 'Binance':
            url = f"https://api.binance.com/api/v3/ticker/price?symbol={symbol_format}"
            response = requests.get(url, headers=headers, timeout=3)
            if response.status_code == 200:
                data = response.json()
                return float(data['price'])

        elif exchange == 'OKX':
            url = f"https://www.okx.com/api/v5/market/ticker?instId={symbol_format}"
            response = requests.get(url, headers=headers, timeout=3)
            if response.status_code == 200:
                data = response.json()
                if data.get('data'):
                    return float(data['data'][0]['last'])

        elif exchange == 'Bybit':
            url = f"https://api.bybit.com/v5/market/tickers?category=spot&symbol={symbol_format}"
            response = requests.get(url, headers=headers, timeout=3)
            if response.status_code == 200:
                data = response.json()
                if data.get('result', {}).get('list'):
                    return float(data['result']['list'][0]['lastPrice'])

        elif exchange == 'KuCoin':
            url = f"https://api.kucoin.com/api/v1/market/orderbook/level1?symbol={symbol_format}"
            response = requests.get(url, headers=headers, timeout=3)
            if response.status_code == 200:
                data = response.json()
                if data.get('code') == '200000' and 'data' in data:
                    return float(data['data']['price'])

        elif exchange == 'Gate.io':
            url = f"https://api.gateio.ws/api/v4/spot/tickers?currency_pair={symbol_format}"
            response = requests.get(url, headers=headers, timeout=3)
            if response.status_code == 200:
                data = response.json()
                if data:
                    return float(data[0]['last'])

        elif exchange == 'Coinbase':
            # 使用公开API获取价格
            url = f"https://api.coinbase.com/v2/exchange-rates?currency={crypto_symbol}"
            response = requests.get(url, headers=headers, timeout=3)
            if response.status_code == 200:
                data = response.json()
                if 'data' in data and 'rates' in data['data']:
                    usd_rate = data['data']['rates'].get('USD')
                    if usd_rate:
                        return float(usd_rate)

        elif exchange == 'Kraken':
            # 转换symbol格式
            kraken_symbol = symbol_format.replace('/', '').replace('-', '')
            if kraken_symbol == 'BTCUSD':
                kraken_symbol = 'XXBTZUSD'
            elif kraken_symbol == 'ETHUSD':
                kraken_symbol = 'XETHZUSD'

            url = f"https://api.kraken.com/0/public/Ticker?pair={kraken_symbol}"
            response = requests.get(url, headers=headers, timeout=3)
            if response.status_code == 200:
                data = response.json()
                if 'result' in data and data['result']:
                    pair_data = list(data['result'].values())[0]
                    if 'c' in pair_data:
                        return float(pair_data['c'][0])

    except Exception as e:
        logging.debug(f"{exchange} 获取 {crypto_symbol} 价格失败: {str(e)}")
        return None

def calculate_arbitrage_opportunity(prices):
    """计算套利机会"""
    if len(prices) < 2:
        return None

    price_values = [data['price'] for data in prices.values()]
    max_price = max(price_values)
    min_price = min(price_values)
    price_diff = max_price - min_price
    percentage_diff = (price_diff / min_price) * 100

    # 找出最高价和最低价的交易所
    max_exchange = next(ex for ex, data in prices.items() if data['price'] == max_price)
    min_exchange = next(ex for ex, data in prices.items() if data['price'] == min_price)

    return {
        'max_exchange': max_exchange,
        'max_price': max_price,
        'min_exchange': min_exchange,
        'min_price': min_price,
        'price_diff': price_diff,
        'percentage_diff': percentage_diff,
        'exchange_count': len(prices)
    }

def analyze_best_opportunity(opportunities):
    """分析最佳套利机会"""
    print("\n" + "="*80)
    print("📊 本轮套利机会排行榜")
    print("="*80)

    # 按套利百分比排序
    sorted_opportunities = sorted(
        opportunities.items(),
        key=lambda x: x[1]['arbitrage']['percentage_diff'],
        reverse=True
    )

    # 显示前5名
    for i, (crypto, data) in enumerate(sorted_opportunities[:5], 1):
        arb = data['arbitrage']
        info = data['info']

        print(f"{i}. 🏆 {info['name']} ({crypto})")
        print(f"   💎 套利空间: {arb['percentage_diff']:.3f}%")
        print(f"   🔥 买入: {arb['min_exchange']} ${arb['min_price']:.4f}")
        print(f"   💰 卖出: {arb['max_exchange']} ${arb['max_price']:.4f}")
        print(f"   💵 利润: ${arb['price_diff']:.4f}")
        print()

    # 突出显示最佳机会
    if sorted_opportunities:
        best = sorted_opportunities[0]
        best_crypto = best[0]
        best_data = best[1]
        best_arb = best_data['arbitrage']

        print("🎯 " + "="*76)
        print(f"🚀 最佳套利机会: {best_data['info']['name']} ({best_crypto})")
        print(f"💎 套利空间: {best_arb['percentage_diff']:.3f}%")
        print("最高价格        | 最低价格          | 相差")
        print(f"{best_arb['max_exchange']}: ${best_arb['max_price']:.4f}    | {best_arb['min_exchange']}: ${best_arb['min_price']:.4f} | ${best_arb['price_diff']:.4f}")
        print("🎯 " + "="*76)

def main():
    """主函数 - 支持多种加密货币选择"""
    print("🚀 多交易所实时价格监控系统")
    print("="*50)
    print("支持的主流加密货币:")
    print("1. BTC  - 比特币 (Bitcoin)")
    print("2. ETH  - 以太坊 (Ethereum)")
    print("3. BNB  - 币安币 (Binance Coin)")
    print("4. XRP  - 瑞波币 (Ripple)")
    print("5. ADA  - 卡尔达诺 (Cardano)")
    print("6. DOGE - 狗狗币 (Dogecoin)")
    print("7. SOL  - 索拉纳 (Solana)")
    print("8. TRX  - 波场 (Tron)")
    print("9. DOT  - 波卡 (Polkadot)")
    print("10. AVAX - 雪崩 (Avalanche)")
    print("11. 扫描最大的套利货币")
    print("="*50)

    # 预设的加密货币配置
    crypto_configs = {
        "1": {"symbol": "BTC", "base": "USDT", "name": "比特币"},
        "2": {"symbol": "ETH", "base": "USDT", "name": "以太坊"},
        "3": {"symbol": "BNB", "base": "USDT", "name": "币安币"},
        "4": {"symbol": "XRP", "base": "USDT", "name": "瑞波币"},
        "5": {"symbol": "ADA", "base": "USDT", "name": "卡尔达诺"},
        "6": {"symbol": "DOGE", "base": "USDT", "name": "狗狗币"},
        "7": {"symbol": "SOL", "base": "USDT", "name": "索拉纳"},
        "8": {"symbol": "TRX", "base": "USDT", "name": "波场"},
        "9": {"symbol": "DOT", "base": "USDT", "name": "波卡"},
        "10": {"symbol": "AVAX", "base": "USDT", "name": "雪崩"},
        "11": {"symbol": "AUTO_SCAN", "base": "USDT", "name": "扫描最大的套利货币"}
    }

    # 用户选择
    while True:
        try:
            choice = input("\n请选择要监控的加密货币 (1-11) 或输入 'q' 退出: ").strip()

            if choice.lower() == 'q':
                print("👋 再见！")
                return

            if choice in crypto_configs:
                config = crypto_configs[choice]

                # 处理自动扫描选项
                if choice == "11":
                    print(f"\n✅ 已选择: {config['name']}")
                    print("🔄 正在启动自动套利扫描系统...")
                    start_auto_arbitrage_scan()
                else:
                    print(f"\n✅ 已选择: {config['name']} ({config['symbol']}/{config['base']})")
                    print("🔄 正在启动监控系统...")

                    # 创建监控实例
                    monitor = MultiExchangePriceMonitor(
                        symbol=config['symbol'],
                        base_currency=config['base']
                    )

                    try:
                        threads = monitor.start()

                        print("📊 监控系统已启动！按 Ctrl+C 停止监控")
                        # 主线程保持运行
                        while True:
                            time.sleep(1)

                    except KeyboardInterrupt:
                        monitor.close()
                        print(f"\n⏹️  {config['name']} 监控已停止")

                        # 询问是否继续监控其他货币
                        continue_choice = input("\n是否要监控其他加密货币? (y/n): ").strip().lower()
                        if continue_choice != 'y':
                            print("👋 程序已退出")
                            return
                        else:
                            continue

            else:
                print("❌ 无效选择，请输入 1-11 之间的数字")

        except Exception as e:
            print(f"❌ 发生错误: {str(e)}")
            continue

if __name__ == "__main__":
    main()
