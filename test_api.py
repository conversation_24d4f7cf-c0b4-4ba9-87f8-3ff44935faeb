#!/usr/bin/env python3
"""
测试交易所API连接
"""

import requests
import time

def test_binance():
    """测试Binance API"""
    try:
        url = "https://api.binance.com/api/v3/ticker/price?symbol=BTCUSDT"
        response = requests.get(url, timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Binance BTC: ${float(data['price']):.2f}")
            return True
    except Exception as e:
        print(f"❌ Binance 失败: {e}")
    return False

def test_okx():
    """测试OKX API"""
    try:
        url = "https://www.okx.com/api/v5/market/ticker?instId=BTC-USDT"
        response = requests.get(url, timeout=5)
        if response.status_code == 200:
            data = response.json()
            if data.get('data'):
                price = float(data['data'][0]['last'])
                print(f"✅ OKX BTC: ${price:.2f}")
                return True
    except Exception as e:
        print(f"❌ OKX 失败: {e}")
    return False

def test_mexc():
    """测试MEXC API"""
    try:
        url = "https://api.mexc.com/api/v3/ticker/price?symbol=BTCUSDT"
        response = requests.get(url, timeout=5)
        if response.status_code == 200:
            data = response.json()
            price = float(data['price'])
            print(f"✅ MEXC BTC: ${price:.2f}")
            return True
    except Exception as e:
        print(f"❌ MEXC 失败: {e}")
    return False

def test_gemini():
    """测试Gemini API"""
    try:
        url = "https://api.gemini.com/v1/pubticker/btcusd"
        response = requests.get(url, timeout=5)
        if response.status_code == 200:
            data = response.json()
            price = float(data['last'])
            print(f"✅ Gemini BTC: ${price:.2f}")
            return True
    except Exception as e:
        print(f"❌ Gemini 失败: {e}")
    return False

def main():
    print("🔍 测试交易所API连接...")
    print("="*50)
    
    success_count = 0
    total_count = 4
    
    if test_binance():
        success_count += 1
    
    if test_okx():
        success_count += 1
        
    if test_mexc():
        success_count += 1
        
    if test_gemini():
        success_count += 1
    
    print("="*50)
    print(f"📊 测试结果: {success_count}/{total_count} 个交易所连接成功")
    
    if success_count >= 2:
        print("✅ 足够的交易所连接成功，可以进行套利分析")
    else:
        print("❌ 连接成功的交易所太少，可能影响数据质量")

if __name__ == "__main__":
    main()
